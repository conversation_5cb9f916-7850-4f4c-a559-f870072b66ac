#!/usr/bin/env python3
"""
Script pour ajouter des données de test à l'application GSlim
"""

import sys
from pathlib import Path

# Ajouter le répertoire src au path Python
sys.path.insert(0, str(Path(__file__).parent / "src"))

def add_test_data():
    """Ajouter des données de test"""
    print("📊 Ajout de données de test...")
    
    try:
        from database.manager import DatabaseManager
        from controllers.article_controller import ArticleController
        from controllers.supplier_controller import SupplierController
        
        # Initialiser la base de données
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        article_controller = ArticleController(db_manager)
        supplier_controller = SupplierController(db_manager)
        
        # Ajouter des fournisseurs de test
        suppliers_data = [
            {
                'name': 'TechSupply Pro',
                'company_name': 'TechSupply Pro SARL',
                'contact_person': '<PERSON>',
                'email': '<EMAIL>',
                'phone': '***********.89',
                'address': '123 Rue de la Technologie',
                'city': 'Paris',
                'postal_code': '75001',
                'rating': 4,
                'category': 'Informatique'
            },
            {
                'name': 'Bureau Plus',
                'company_name': 'Bureau Plus SA',
                'contact_person': 'Marie <PERSON>',
                'email': '<EMAIL>',
                'phone': '***********.32',
                'address': '456 Avenue du Bureau',
                'city': 'Lyon',
                'postal_code': '69000',
                'rating': 5,
                'category': 'Bureautique'
            },
            {
                'name': 'Matériel Express',
                'contact_person': 'Pierre Durand',
                'email': '<EMAIL>',
                'phone': '***********.44',
                'rating': 3,
                'category': 'Général'
            }
        ]
        
        created_suppliers = []
        for supplier_data in suppliers_data:
            result = supplier_controller.create_supplier(supplier_data)
            if result.success:
                created_suppliers.append(result.data)
                print(f"  ✅ Fournisseur créé: {supplier_data['name']}")
            else:
                print(f"  ❌ Erreur fournisseur {supplier_data['name']}: {result.message}")
        
        # Ajouter des articles de test
        articles_data = [
            {
                'name': 'Ordinateur portable Dell Latitude',
                'reference': 'DELL-LAT-001',
                'description': 'Ordinateur portable professionnel 15.6" Intel i5, 8GB RAM, 256GB SSD',
                'supplier_id': created_suppliers[0]['id'] if created_suppliers else None,
                'supplier_name': 'TechSupply Pro',
                'unit_price': 899.99,
                'quantity_in_stock': 15,
                'minimum_stock': 5,
                'maximum_stock': 50,
                'unit': 'pièce',
                'category_name': 'Informatique'
            },
            {
                'name': 'Souris optique sans fil',
                'reference': 'MOUSE-001',
                'description': 'Souris optique sans fil ergonomique',
                'supplier_id': created_suppliers[0]['id'] if created_suppliers else None,
                'supplier_name': 'TechSupply Pro',
                'unit_price': 25.50,
                'quantity_in_stock': 50,
                'minimum_stock': 10,
                'unit': 'pièce',
                'category_name': 'Informatique'
            },
            {
                'name': 'Clavier mécanique',
                'reference': 'KEYB-001',
                'description': 'Clavier mécanique rétroéclairé',
                'supplier_id': created_suppliers[0]['id'] if created_suppliers else None,
                'supplier_name': 'TechSupply Pro',
                'unit_price': 89.99,
                'quantity_in_stock': 2,  # Stock bas
                'minimum_stock': 5,
                'unit': 'pièce',
                'category_name': 'Informatique'
            },
            {
                'name': 'Ramette papier A4',
                'reference': 'PAP-A4-001',
                'description': 'Ramette papier blanc A4 80g - 500 feuilles',
                'supplier_id': created_suppliers[1]['id'] if len(created_suppliers) > 1 else None,
                'supplier_name': 'Bureau Plus',
                'unit_price': 4.99,
                'quantity_in_stock': 100,
                'minimum_stock': 20,
                'unit': 'ramette',
                'category_name': 'Bureautique'
            },
            {
                'name': 'Stylos bille bleus',
                'reference': 'STYLO-001',
                'description': 'Lot de 10 stylos bille bleus',
                'supplier_id': created_suppliers[1]['id'] if len(created_suppliers) > 1 else None,
                'supplier_name': 'Bureau Plus',
                'unit_price': 8.50,
                'quantity_in_stock': 0,  # Rupture de stock
                'minimum_stock': 5,
                'unit': 'lot',
                'category_name': 'Bureautique'
            },
            {
                'name': 'Chaise de bureau ergonomique',
                'reference': 'CHAIR-001',
                'description': 'Chaise de bureau ergonomique avec accoudoirs réglables',
                'supplier_id': created_suppliers[2]['id'] if len(created_suppliers) > 2 else None,
                'supplier_name': 'Matériel Express',
                'unit_price': 199.99,
                'quantity_in_stock': 8,
                'minimum_stock': 3,
                'unit': 'pièce',
                'category_name': 'Mobilier'
            },
            {
                'name': 'Écran 24 pouces Full HD',
                'reference': 'SCREEN-24-001',
                'description': 'Écran LED 24" Full HD 1920x1080 avec pied réglable',
                'supplier_id': created_suppliers[0]['id'] if created_suppliers else None,
                'supplier_name': 'TechSupply Pro',
                'unit_price': 159.99,
                'quantity_in_stock': 12,
                'minimum_stock': 4,
                'unit': 'pièce',
                'category_name': 'Informatique'
            },
            {
                'name': 'Cartouches d\'encre noire',
                'reference': 'INK-BLACK-001',
                'description': 'Cartouches d\'encre noire compatibles HP',
                'supplier_id': created_suppliers[1]['id'] if len(created_suppliers) > 1 else None,
                'supplier_name': 'Bureau Plus',
                'unit_price': 15.99,
                'quantity_in_stock': 25,
                'minimum_stock': 8,
                'unit': 'pièce',
                'category_name': 'Consommables'
            }
        ]
        
        created_articles = []
        for article_data in articles_data:
            result = article_controller.create_article(article_data)
            if result.success:
                created_articles.append(result.data)
                print(f"  ✅ Article créé: {article_data['name']}")
            else:
                print(f"  ❌ Erreur article {article_data['name']}: {result.message}")
        
        # Afficher les statistiques
        stats = article_controller.get_stock_statistics()
        print(f"\n📈 Statistiques:")
        print(f"  • Total articles: {stats['total_articles']}")
        print(f"  • Articles en stock bas: {stats['low_stock']}")
        print(f"  • Articles en rupture: {stats['out_of_stock']}")
        print(f"  • Valeur totale du stock: {stats['total_value']:.2f} €")
        
        db_manager.close()
        
        print(f"\n🎉 Données de test ajoutées avec succès !")
        print(f"  • {len(created_suppliers)} fournisseurs")
        print(f"  • {len(created_articles)} articles")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de l'ajout des données: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale"""
    print("🗄️ Ajout de données de test pour GSlim")
    print("=" * 50)
    
    success = add_test_data()
    
    if success:
        print("\n💡 Vous pouvez maintenant lancer l'application:")
        print("   python main.py")
        print("   Connectez-vous avec admin/admin123")
        print("   Naviguez vers 'Articles' pour voir les données")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
