"""
Contrôleur de base pour tous les contrôleurs GSlim
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from decimal import Decimal

from database.manager import DatabaseManager
from utils.logger import setup_logger


class BaseController(ABC):
    """Classe de base pour tous les contrôleurs"""
    
    def __init__(self, db_manager: DatabaseManager):
        """Initialiser le contrôleur"""
        self.db_manager = db_manager
        self.logger = setup_logger(self.__class__.__name__)
    
    @abstractmethod
    def get_table_name(self) -> str:
        """Retourner le nom de la table (à implémenter dans chaque contrôleur)"""
        pass
    
    def create(self, data: Dict[str, Any]) -> Tuple[bool, Any]:
        """
        Créer un nouvel enregistrement
        
        Returns:
            Tuple[bool, Any]: (succès, id_créé ou message_erreur)
        """
        try:
            # Préparer les données
            data = self._prepare_data_for_insert(data)
            
            # Valider les données
            validation_result = self._validate_data(data)
            if not validation_result[0]:
                return False, validation_result[1]
            
            # Construire la requête d'insertion
            table_name = self.get_table_name()
            columns = list(data.keys())
            placeholders = ', '.join(['?' for _ in columns])
            values = [self._convert_value_for_db(v) for v in data.values()]
            
            query = f"""
                INSERT INTO {table_name} ({', '.join(columns)})
                VALUES ({placeholders})
            """
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, values)
                new_id = cursor.lastrowid
                conn.commit()
                
                self.logger.info(f"Enregistrement créé dans {table_name} avec l'ID {new_id}")
                return True, new_id
                
        except Exception as e:
            self.logger.error(f"Erreur lors de la création dans {self.get_table_name()}: {e}")
            return False, str(e)
    
    def get_by_id(self, record_id: int) -> Optional[Dict[str, Any]]:
        """Récupérer un enregistrement par son ID"""
        try:
            table_name = self.get_table_name()
            query = f"SELECT * FROM {table_name} WHERE id = ?"
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, (record_id,))
                row = cursor.fetchone()
                
                if row:
                    return dict(row)
                return None
                
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération de l'ID {record_id}: {e}")
            return None
    
    def get_all(self, filters: Dict[str, Any] = None, 
                order_by: str = None, limit: int = None) -> List[Dict[str, Any]]:
        """Récupérer tous les enregistrements avec filtres optionnels"""
        try:
            table_name = self.get_table_name()
            query = f"SELECT * FROM {table_name}"
            params = []
            
            # Ajouter les filtres
            if filters:
                where_conditions = []
                for key, value in filters.items():
                    if value is not None:
                        where_conditions.append(f"{key} = ?")
                        params.append(value)
                
                if where_conditions:
                    query += " WHERE " + " AND ".join(where_conditions)
            
            # Ajouter l'ordre
            if order_by:
                query += f" ORDER BY {order_by}"
            
            # Ajouter la limite
            if limit:
                query += f" LIMIT {limit}"
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
                return [dict(row) for row in rows]
                
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des enregistrements: {e}")
            return []
    
    def update(self, record_id: int, data: Dict[str, Any]) -> Tuple[bool, str]:
        """
        Mettre à jour un enregistrement
        
        Returns:
            Tuple[bool, str]: (succès, message)
        """
        try:
            # Vérifier que l'enregistrement existe
            if not self.get_by_id(record_id):
                return False, "Enregistrement non trouvé"
            
            # Préparer les données
            data = self._prepare_data_for_update(data)
            
            # Valider les données
            validation_result = self._validate_data(data, is_update=True)
            if not validation_result[0]:
                return False, validation_result[1]
            
            # Ajouter updated_at si disponible
            if 'updated_at' not in data:
                data['updated_at'] = datetime.now()
            
            # Construire la requête de mise à jour
            table_name = self.get_table_name()
            set_clauses = [f"{key} = ?" for key in data.keys()]
            values = [self._convert_value_for_db(v) for v in data.values()] + [record_id]
            
            query = f"""
                UPDATE {table_name}
                SET {', '.join(set_clauses)}
                WHERE id = ?
            """
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, values)
                conn.commit()
                
                if cursor.rowcount > 0:
                    self.logger.info(f"Enregistrement {record_id} mis à jour dans {table_name}")
                    return True, "Mise à jour réussie"
                else:
                    return False, "Aucune modification effectuée"
                    
        except Exception as e:
            self.logger.error(f"Erreur lors de la mise à jour de l'ID {record_id}: {e}")
            return False, str(e)
    
    def delete(self, record_id: int) -> Tuple[bool, str]:
        """
        Supprimer un enregistrement
        
        Returns:
            Tuple[bool, str]: (succès, message)
        """
        try:
            # Vérifier que l'enregistrement existe
            if not self.get_by_id(record_id):
                return False, "Enregistrement non trouvé"
            
            # Vérifier les contraintes avant suppression
            constraint_check = self._check_delete_constraints(record_id)
            if not constraint_check[0]:
                return False, constraint_check[1]
            
            table_name = self.get_table_name()
            query = f"DELETE FROM {table_name} WHERE id = ?"
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, (record_id,))
                conn.commit()
                
                if cursor.rowcount > 0:
                    self.logger.info(f"Enregistrement {record_id} supprimé de {table_name}")
                    return True, "Suppression réussie"
                else:
                    return False, "Aucune suppression effectuée"
                    
        except Exception as e:
            self.logger.error(f"Erreur lors de la suppression de l'ID {record_id}: {e}")
            return False, str(e)
    
    def search(self, search_term: str, fields: List[str] = None) -> List[Dict[str, Any]]:
        """Rechercher des enregistrements"""
        try:
            if not search_term.strip():
                return self.get_all()
            
            table_name = self.get_table_name()
            
            # Utiliser les champs par défaut si non spécifiés
            if not fields:
                fields = self._get_searchable_fields()
            
            # Construire la requête de recherche
            search_conditions = []
            params = []
            
            for field in fields:
                search_conditions.append(f"{field} LIKE ?")
                params.append(f"%{search_term}%")
            
            query = f"""
                SELECT * FROM {table_name}
                WHERE {' OR '.join(search_conditions)}
                ORDER BY {self._get_default_order()}
            """
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
                return [dict(row) for row in rows]
                
        except Exception as e:
            self.logger.error(f"Erreur lors de la recherche '{search_term}': {e}")
            return []
    
    def count(self, filters: Dict[str, Any] = None) -> int:
        """Compter les enregistrements"""
        try:
            table_name = self.get_table_name()
            query = f"SELECT COUNT(*) as count FROM {table_name}"
            params = []
            
            if filters:
                where_conditions = []
                for key, value in filters.items():
                    if value is not None:
                        where_conditions.append(f"{key} = ?")
                        params.append(value)
                
                if where_conditions:
                    query += " WHERE " + " AND ".join(where_conditions)
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                result = cursor.fetchone()
                
                return result['count'] if result else 0
                
        except Exception as e:
            self.logger.error(f"Erreur lors du comptage: {e}")
            return 0
    
    # Méthodes à implémenter dans les contrôleurs spécifiques
    def _prepare_data_for_insert(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Préparer les données pour l'insertion (à surcharger si nécessaire)"""
        # Supprimer l'ID s'il est présent
        data = data.copy()
        data.pop('id', None)
        
        # Ajouter les timestamps
        now = datetime.now()
        if 'created_at' not in data:
            data['created_at'] = now
        if 'updated_at' not in data:
            data['updated_at'] = now
        
        return data
    
    def _prepare_data_for_update(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Préparer les données pour la mise à jour (à surcharger si nécessaire)"""
        # Supprimer les champs non modifiables
        data = data.copy()
        data.pop('id', None)
        data.pop('created_at', None)
        
        return data
    
    def _validate_data(self, data: Dict[str, Any], is_update: bool = False) -> Tuple[bool, str]:
        """Valider les données (à surcharger dans chaque contrôleur)"""
        return True, "Validation réussie"
    
    def _check_delete_constraints(self, record_id: int) -> Tuple[bool, str]:
        """Vérifier les contraintes avant suppression (à surcharger si nécessaire)"""
        return True, "Suppression autorisée"
    
    def _get_searchable_fields(self) -> List[str]:
        """Retourner les champs recherchables (à surcharger dans chaque contrôleur)"""
        return ['name']
    
    def _get_default_order(self) -> str:
        """Retourner l'ordre par défaut (à surcharger si nécessaire)"""
        return "created_at DESC"

    def _convert_value_for_db(self, value):
        """Convertir une valeur pour la base de données"""
        if isinstance(value, Decimal):
            return float(value)
        elif isinstance(value, datetime):
            return value.isoformat()
        return value


class CRUDResult:
    """Classe pour encapsuler les résultats des opérations CRUD"""

    def __init__(self, success: bool, data: Any = None, message: str = "", errors: Dict[str, List[str]] = None):
        self.success = success
        self.data = data
        self.message = message
        self.errors = errors or {}

    def __bool__(self):
        return self.success

    def __str__(self):
        return self.message
