"""
Contrôleur pour la génération de rapports
Gère la création de rapports PDF, Excel et graphiques
"""

import os
from typing import List, Dict, Any, Optional
from datetime import datetime, date, timedelta
from decimal import Decimal
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.backends.backend_pdf import PdfPages
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch

from .base import BaseController, CRUDResult
from utils.logger import setup_logger
from config.settings import config


class ReportController(BaseController):
    """Contrôleur pour la génération de rapports"""

    def __init__(self, db_manager):
        super().__init__(db_manager)
        self.logger = setup_logger(__name__)
        self.logger.info("ReportController initialisé")

        # Créer le répertoire des rapports s'il n'existe pas
        self.reports_dir = "reports"
        os.makedirs(self.reports_dir, exist_ok=True)

    def get_table_name(self):
        """Retourner le nom de la table (aucune pour les rapports)"""
        return None
    
    def generate_stock_report(self, format_type: str = "pdf", include_charts: bool = True) -> CRUDResult:
        """Générer un rapport de stock complet"""
        try:
            # Récupérer les données
            stock_data = self._get_stock_data()
            if not stock_data:
                return CRUDResult(False, None, "Aucune donnée de stock disponible")
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            if format_type.lower() == "pdf":
                filename = f"rapport_stock_{timestamp}.pdf"
                filepath = os.path.join(self.reports_dir, filename)
                success = self._generate_stock_pdf(stock_data, filepath, include_charts)
            elif format_type.lower() == "excel":
                filename = f"rapport_stock_{timestamp}.xlsx"
                filepath = os.path.join(self.reports_dir, filename)
                success = self._generate_stock_excel(stock_data, filepath, include_charts)
            else:
                return CRUDResult(False, None, "Format non supporté. Utilisez 'pdf' ou 'excel'")
            
            if success:
                self.logger.info(f"Rapport de stock généré: {filename}")
                return CRUDResult(True, filepath, f"Rapport généré: {filename}")
            else:
                return CRUDResult(False, None, "Erreur lors de la génération du rapport")
                
        except Exception as e:
            self.logger.error(f"Erreur lors de la génération du rapport de stock: {e}")
            return CRUDResult(False, None, f"Erreur: {e}")
    
    def generate_movements_report(self, start_date: date, end_date: date, format_type: str = "pdf") -> CRUDResult:
        """Générer un rapport des mouvements de stock"""
        try:
            # Récupérer les données des mouvements
            movements_data = self._get_movements_data(start_date, end_date)
            if not movements_data:
                return CRUDResult(False, None, "Aucun mouvement trouvé pour cette période")
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            period_str = f"{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}"
            
            if format_type.lower() == "pdf":
                filename = f"rapport_mouvements_{period_str}_{timestamp}.pdf"
                filepath = os.path.join(self.reports_dir, filename)
                success = self._generate_movements_pdf(movements_data, filepath, start_date, end_date)
            elif format_type.lower() == "excel":
                filename = f"rapport_mouvements_{period_str}_{timestamp}.xlsx"
                filepath = os.path.join(self.reports_dir, filename)
                success = self._generate_movements_excel(movements_data, filepath, start_date, end_date)
            else:
                return CRUDResult(False, None, "Format non supporté. Utilisez 'pdf' ou 'excel'")
            
            if success:
                self.logger.info(f"Rapport des mouvements généré: {filename}")
                return CRUDResult(True, filepath, f"Rapport généré: {filename}")
            else:
                return CRUDResult(False, None, "Erreur lors de la génération du rapport")
                
        except Exception as e:
            self.logger.error(f"Erreur lors de la génération du rapport des mouvements: {e}")
            return CRUDResult(False, None, f"Erreur: {e}")
    
    def generate_suppliers_report(self, format_type: str = "pdf") -> CRUDResult:
        """Générer un rapport des fournisseurs"""
        try:
            # Récupérer les données des fournisseurs
            suppliers_data = self._get_suppliers_data()
            if not suppliers_data:
                return CRUDResult(False, None, "Aucun fournisseur trouvé")
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            if format_type.lower() == "pdf":
                filename = f"rapport_fournisseurs_{timestamp}.pdf"
                filepath = os.path.join(self.reports_dir, filename)
                success = self._generate_suppliers_pdf(suppliers_data, filepath)
            elif format_type.lower() == "excel":
                filename = f"rapport_fournisseurs_{timestamp}.xlsx"
                filepath = os.path.join(self.reports_dir, filename)
                success = self._generate_suppliers_excel(suppliers_data, filepath)
            else:
                return CRUDResult(False, None, "Format non supporté. Utilisez 'pdf' ou 'excel'")
            
            if success:
                self.logger.info(f"Rapport des fournisseurs généré: {filename}")
                return CRUDResult(True, filepath, f"Rapport généré: {filename}")
            else:
                return CRUDResult(False, None, "Erreur lors de la génération du rapport")
                
        except Exception as e:
            self.logger.error(f"Erreur lors de la génération du rapport des fournisseurs: {e}")
            return CRUDResult(False, None, f"Erreur: {e}")
    
    def generate_dashboard_charts(self) -> Dict[str, str]:
        """Générer les graphiques pour le tableau de bord"""
        try:
            charts = {}
            
            # Graphique de l'évolution du stock
            stock_evolution_path = self._create_stock_evolution_chart()
            if stock_evolution_path:
                charts['stock_evolution'] = stock_evolution_path
            
            # Graphique des mouvements par type
            movements_chart_path = self._create_movements_by_type_chart()
            if movements_chart_path:
                charts['movements_by_type'] = movements_chart_path
            
            # Graphique des articles les plus vendus
            top_articles_path = self._create_top_articles_chart()
            if top_articles_path:
                charts['top_articles'] = top_articles_path
            
            # Graphique de la valeur du stock par catégorie
            stock_value_path = self._create_stock_value_by_category_chart()
            if stock_value_path:
                charts['stock_value_by_category'] = stock_value_path
            
            return charts
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la génération des graphiques: {e}")
            return {}
    
    def _get_stock_data(self) -> List[Dict[str, Any]]:
        """Récupérer les données de stock"""
        try:
            cursor = self.db_manager.cursor()

            query = """
                SELECT
                    a.*,
                    s.name as supplier_name,
                    c.name as category_name,
                    (a.quantity_in_stock * a.unit_price) as stock_value,
                    CASE
                        WHEN a.quantity_in_stock = 0 THEN 'Rupture'
                        WHEN a.quantity_in_stock <= a.minimum_stock THEN 'Stock bas'
                        ELSE 'Normal'
                    END as stock_status
                FROM articles a
                LEFT JOIN suppliers s ON a.supplier_id = s.id
                LEFT JOIN categories c ON a.category_id = c.id
                WHERE a.is_active = 1
                ORDER BY a.name
            """
            
            cursor.execute(query)
            articles = cursor.fetchall()
            
            return [dict(article) for article in articles]
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des données de stock: {e}")
            return []
    
    def _get_movements_data(self, start_date: date, end_date: date) -> List[Dict[str, Any]]:
        """Récupérer les données des mouvements"""
        try:
            cursor = self.db_manager.cursor()

            query = """
                SELECT * FROM stock_movements
                WHERE DATE(movement_date) BETWEEN ? AND ?
                ORDER BY movement_date DESC, created_at DESC
            """
            
            cursor.execute(query, (start_date.isoformat(), end_date.isoformat()))
            movements = cursor.fetchall()
            
            return [dict(movement) for movement in movements]
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des mouvements: {e}")
            return []
    
    def _get_suppliers_data(self) -> List[Dict[str, Any]]:
        """Récupérer les données des fournisseurs"""
        try:
            cursor = self.db_manager.cursor()

            query = """
                SELECT
                    s.*,
                    COUNT(a.id) as articles_count,
                    COUNT(o.id) as orders_count,
                    COALESCE(SUM(o.total_amount), 0) as total_orders_value
                FROM suppliers s
                LEFT JOIN articles a ON s.id = a.supplier_id AND a.is_active = 1
                LEFT JOIN orders o ON s.id = o.supplier_id
                WHERE s.is_active = 1
                GROUP BY s.id
                ORDER BY s.name
            """
            
            cursor.execute(query)
            suppliers = cursor.fetchall()
            
            return [dict(supplier) for supplier in suppliers]
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des fournisseurs: {e}")
            return []
    
    def _generate_stock_pdf(self, data: List[Dict[str, Any]], filepath: str, include_charts: bool) -> bool:
        """Générer un rapport PDF de stock"""
        try:
            doc = SimpleDocTemplate(filepath, pagesize=A4)
            styles = getSampleStyleSheet()
            story = []
            
            # Titre
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=1  # Centré
            )
            story.append(Paragraph(f"Rapport de Stock - {datetime.now().strftime('%d/%m/%Y')}", title_style))
            story.append(Spacer(1, 20))
            
            # Résumé
            total_articles = len(data)
            total_value = sum(item.get('stock_value', 0) for item in data)
            low_stock = len([item for item in data if item.get('stock_status') == 'Stock bas'])
            out_of_stock = len([item for item in data if item.get('stock_status') == 'Rupture'])
            
            summary_data = [
                ['Indicateur', 'Valeur'],
                ['Total articles', str(total_articles)],
                ['Valeur totale du stock', f"{total_value:.2f} €"],
                ['Articles en stock bas', str(low_stock)],
                ['Articles en rupture', str(out_of_stock)]
            ]
            
            summary_table = Table(summary_data)
            summary_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 14),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(summary_table)
            story.append(Spacer(1, 30))
            
            # Tableau détaillé
            story.append(Paragraph("Détail des Articles", styles['Heading2']))
            story.append(Spacer(1, 12))
            
            # Préparer les données du tableau
            table_data = [['Nom', 'Référence', 'Stock', 'Prix', 'Valeur', 'Statut']]
            
            for item in data:
                table_data.append([
                    item.get('name', ''),
                    item.get('reference', ''),
                    str(item.get('quantity_in_stock', 0)),
                    f"{item.get('unit_price', 0):.2f} €",
                    f"{item.get('stock_value', 0):.2f} €",
                    item.get('stock_status', '')
                ])
            
            detail_table = Table(table_data)
            detail_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('FONTSIZE', (0, 1), (-1, -1), 8)
            ]))
            
            story.append(detail_table)
            
            # Construire le PDF
            doc.build(story)
            return True
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la génération du PDF de stock: {e}")
            return False
    
    def _generate_stock_excel(self, data: List[Dict[str, Any]], filepath: str, include_charts: bool) -> bool:
        """Générer un rapport Excel de stock"""
        try:
            # Créer un DataFrame
            df = pd.DataFrame(data)
            
            # Sélectionner et renommer les colonnes importantes
            columns_mapping = {
                'name': 'Nom',
                'reference': 'Référence',
                'quantity_in_stock': 'Stock',
                'minimum_stock': 'Stock minimum',
                'unit_price': 'Prix unitaire',
                'stock_value': 'Valeur stock',
                'supplier_name': 'Fournisseur',
                'category_name': 'Catégorie',
                'stock_status': 'Statut'
            }
            
            # Filtrer les colonnes existantes
            available_columns = {k: v for k, v in columns_mapping.items() if k in df.columns}
            df_filtered = df[list(available_columns.keys())].rename(columns=available_columns)
            
            # Écrire dans Excel avec plusieurs feuilles
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                # Feuille principale avec tous les articles
                df_filtered.to_excel(writer, sheet_name='Stock Complet', index=False)
                
                # Feuille avec les articles en stock bas
                if 'Statut' in df_filtered.columns:
                    low_stock_df = df_filtered[df_filtered['Statut'] == 'Stock bas']
                    if not low_stock_df.empty:
                        low_stock_df.to_excel(writer, sheet_name='Stock Bas', index=False)
                    
                    # Feuille avec les ruptures
                    out_of_stock_df = df_filtered[df_filtered['Statut'] == 'Rupture']
                    if not out_of_stock_df.empty:
                        out_of_stock_df.to_excel(writer, sheet_name='Ruptures', index=False)
                
                # Feuille de résumé
                summary_data = {
                    'Indicateur': ['Total articles', 'Valeur totale', 'Stock bas', 'Ruptures'],
                    'Valeur': [
                        len(df_filtered),
                        df_filtered['Valeur stock'].sum() if 'Valeur stock' in df_filtered.columns else 0,
                        len(df_filtered[df_filtered['Statut'] == 'Stock bas']) if 'Statut' in df_filtered.columns else 0,
                        len(df_filtered[df_filtered['Statut'] == 'Rupture']) if 'Statut' in df_filtered.columns else 0
                    ]
                }
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='Résumé', index=False)
            
            return True

        except Exception as e:
            self.logger.error(f"Erreur lors de la génération du Excel de stock: {e}")
            return False

    def _create_stock_evolution_chart(self) -> Optional[str]:
        """Créer un graphique de l'évolution du stock"""
        try:
            # Récupérer les données des 30 derniers jours
            cursor = self.db_manager.cursor()

            query = """
                SELECT
                    DATE(movement_date) as date,
                    SUM(CASE WHEN movement_type = 'Entrée' THEN quantity ELSE 0 END) as entries,
                    SUM(CASE WHEN movement_type = 'Sortie' THEN quantity ELSE 0 END) as exits
                FROM stock_movements
                WHERE movement_date >= DATE('now', '-30 days')
                GROUP BY DATE(movement_date)
                ORDER BY date
            """

            cursor.execute(query)
            data = cursor.fetchall()

            if not data:
                return None

            # Créer le graphique avec Plotly
            dates = [row['date'] for row in data]
            entries = [row['entries'] for row in data]
            exits = [row['exits'] for row in data]

            fig = go.Figure()

            fig.add_trace(go.Scatter(
                x=dates, y=entries,
                mode='lines+markers',
                name='Entrées',
                line=dict(color='green')
            ))

            fig.add_trace(go.Scatter(
                x=dates, y=exits,
                mode='lines+markers',
                name='Sorties',
                line=dict(color='red')
            ))

            fig.update_layout(
                title='Évolution des mouvements de stock (30 derniers jours)',
                xaxis_title='Date',
                yaxis_title='Quantité',
                hovermode='x unified'
            )

            # Sauvegarder
            chart_path = os.path.join(self.reports_dir, f"stock_evolution_{datetime.now().strftime('%Y%m%d')}.html")
            fig.write_html(chart_path)

            return chart_path

        except Exception as e:
            self.logger.error(f"Erreur lors de la création du graphique d'évolution: {e}")
            return None

    def _create_movements_by_type_chart(self) -> Optional[str]:
        """Créer un graphique des mouvements par type"""
        try:
            cursor = self.db_manager.cursor()

            query = """
                SELECT
                    movement_type,
                    COUNT(*) as count,
                    SUM(quantity) as total_quantity
                FROM stock_movements
                WHERE movement_date >= DATE('now', '-30 days')
                GROUP BY movement_type
            """

            cursor.execute(query)
            data = cursor.fetchall()

            if not data:
                return None

            # Créer un graphique en secteurs
            labels = [row['movement_type'] for row in data]
            values = [row['total_quantity'] for row in data]

            fig = go.Figure(data=[go.Pie(labels=labels, values=values)])
            fig.update_layout(title='Répartition des mouvements par type (30 derniers jours)')

            # Sauvegarder
            chart_path = os.path.join(self.reports_dir, f"movements_by_type_{datetime.now().strftime('%Y%m%d')}.html")
            fig.write_html(chart_path)

            return chart_path

        except Exception as e:
            self.logger.error(f"Erreur lors de la création du graphique par type: {e}")
            return None

    def _create_top_articles_chart(self) -> Optional[str]:
        """Créer un graphique des articles les plus vendus"""
        try:
            cursor = self.db_manager.cursor()

            query = """
                SELECT
                    article_name,
                    SUM(quantity) as total_sold
                FROM stock_movements
                WHERE movement_type = 'Sortie'
                AND movement_date >= DATE('now', '-30 days')
                GROUP BY article_id, article_name
                ORDER BY total_sold DESC
                LIMIT 10
            """

            cursor.execute(query)
            data = cursor.fetchall()

            if not data:
                return None

            # Créer un graphique en barres
            articles = [row['article_name'] for row in data]
            quantities = [row['total_sold'] for row in data]

            fig = go.Figure([go.Bar(x=articles, y=quantities)])
            fig.update_layout(
                title='Top 10 des articles les plus vendus (30 derniers jours)',
                xaxis_title='Articles',
                yaxis_title='Quantité vendue',
                xaxis_tickangle=-45
            )

            # Sauvegarder
            chart_path = os.path.join(self.reports_dir, f"top_articles_{datetime.now().strftime('%Y%m%d')}.html")
            fig.write_html(chart_path)

            return chart_path

        except Exception as e:
            self.logger.error(f"Erreur lors de la création du graphique top articles: {e}")
            return None

    def _create_stock_value_by_category_chart(self) -> Optional[str]:
        """Créer un graphique de la valeur du stock par catégorie"""
        try:
            cursor = self.db_manager.cursor()

            query = """
                SELECT
                    COALESCE(c.name, 'Sans catégorie') as category_name,
                    SUM(a.quantity_in_stock * a.unit_price) as total_value
                FROM articles a
                LEFT JOIN categories c ON a.category_id = c.id
                WHERE a.is_active = 1
                GROUP BY c.id, c.name
                ORDER BY total_value DESC
            """

            cursor.execute(query)
            data = cursor.fetchall()

            if not data:
                return None

            # Créer un graphique en secteurs
            categories = [row['category_name'] for row in data]
            values = [row['total_value'] for row in data]

            fig = go.Figure(data=[go.Pie(labels=categories, values=values)])
            fig.update_layout(title='Valeur du stock par catégorie')

            # Sauvegarder
            chart_path = os.path.join(self.reports_dir, f"stock_value_by_category_{datetime.now().strftime('%Y%m%d')}.html")
            fig.write_html(chart_path)

            return chart_path

        except Exception as e:
            self.logger.error(f"Erreur lors de la création du graphique par catégorie: {e}")
            return None

    def _generate_movements_pdf(self, data: List[Dict[str, Any]], filepath: str, start_date: date, end_date: date) -> bool:
        """Générer un rapport PDF des mouvements"""
        try:
            doc = SimpleDocTemplate(filepath, pagesize=A4)
            styles = getSampleStyleSheet()
            story = []

            # Titre
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=1
            )
            story.append(Paragraph(f"Rapport des Mouvements de Stock", title_style))
            story.append(Paragraph(f"Période: {start_date.strftime('%d/%m/%Y')} - {end_date.strftime('%d/%m/%Y')}", styles['Normal']))
            story.append(Spacer(1, 20))

            # Résumé
            total_movements = len(data)
            entries = len([m for m in data if m.get('movement_type') == 'Entrée'])
            exits = len([m for m in data if m.get('movement_type') == 'Sortie'])
            adjustments = len([m for m in data if m.get('movement_type') == 'Ajustement'])

            summary_data = [
                ['Type de mouvement', 'Nombre'],
                ['Total mouvements', str(total_movements)],
                ['Entrées', str(entries)],
                ['Sorties', str(exits)],
                ['Ajustements', str(adjustments)]
            ]

            summary_table = Table(summary_data)
            summary_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 14),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(summary_table)
            story.append(Spacer(1, 30))

            # Tableau détaillé
            story.append(Paragraph("Détail des Mouvements", styles['Heading2']))
            story.append(Spacer(1, 12))

            # Préparer les données du tableau (limiter à 50 pour éviter un PDF trop long)
            table_data = [['Date', 'Article', 'Type', 'Quantité', 'Motif']]

            for item in data[:50]:  # Limiter à 50 mouvements
                movement_date = item.get('movement_date', '')
                if movement_date:
                    try:
                        date_obj = datetime.fromisoformat(movement_date.replace('Z', '+00:00'))
                        formatted_date = date_obj.strftime('%d/%m/%Y')
                    except:
                        formatted_date = movement_date[:10]
                else:
                    formatted_date = ''

                table_data.append([
                    formatted_date,
                    item.get('article_name', '')[:20],  # Limiter la longueur
                    item.get('movement_type', ''),
                    str(item.get('quantity', 0)),
                    item.get('movement_reason', '')[:15]  # Limiter la longueur
                ])

            detail_table = Table(table_data)
            detail_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('FONTSIZE', (0, 1), (-1, -1), 8)
            ]))

            story.append(detail_table)

            if len(data) > 50:
                story.append(Spacer(1, 12))
                story.append(Paragraph(f"Note: Seuls les 50 premiers mouvements sont affichés. Total: {len(data)} mouvements.", styles['Normal']))

            # Construire le PDF
            doc.build(story)
            return True

        except Exception as e:
            self.logger.error(f"Erreur lors de la génération du PDF des mouvements: {e}")
            return False

    def _generate_movements_excel(self, data: List[Dict[str, Any]], filepath: str, start_date: date, end_date: date) -> bool:
        """Générer un rapport Excel des mouvements"""
        try:
            df = pd.DataFrame(data)

            # Renommer les colonnes
            columns_mapping = {
                'movement_date': 'Date',
                'article_name': 'Article',
                'article_reference': 'Référence',
                'movement_type': 'Type',
                'movement_reason': 'Motif',
                'quantity': 'Quantité',
                'unit_price': 'Prix unitaire',
                'total_value': 'Valeur totale',
                'user_name': 'Utilisateur'
            }

            available_columns = {k: v for k, v in columns_mapping.items() if k in df.columns}
            df_filtered = df[list(available_columns.keys())].rename(columns=available_columns)

            # Écrire dans Excel
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                df_filtered.to_excel(writer, sheet_name='Mouvements', index=False)

                # Feuille de résumé par type
                if 'Type' in df_filtered.columns:
                    summary_by_type = df_filtered.groupby('Type').agg({
                        'Quantité': ['count', 'sum']
                    }).round(2)
                    summary_by_type.columns = ['Nombre de mouvements', 'Quantité totale']
                    summary_by_type.to_excel(writer, sheet_name='Résumé par type')

            return True

        except Exception as e:
            self.logger.error(f"Erreur lors de la génération du Excel des mouvements: {e}")
            return False

    def _generate_suppliers_pdf(self, data: List[Dict[str, Any]], filepath: str) -> bool:
        """Générer un rapport PDF des fournisseurs"""
        try:
            doc = SimpleDocTemplate(filepath, pagesize=A4)
            styles = getSampleStyleSheet()
            story = []

            # Titre
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=1
            )
            story.append(Paragraph(f"Rapport des Fournisseurs - {datetime.now().strftime('%d/%m/%Y')}", title_style))
            story.append(Spacer(1, 20))

            # Tableau des fournisseurs
            table_data = [['Nom', 'Contact', 'Email', 'Téléphone', 'Articles', 'Commandes']]

            for supplier in data:
                table_data.append([
                    supplier.get('name', ''),
                    supplier.get('contact_person', ''),
                    supplier.get('email', ''),
                    supplier.get('phone', ''),
                    str(supplier.get('articles_count', 0)),
                    str(supplier.get('orders_count', 0))
                ])

            suppliers_table = Table(table_data)
            suppliers_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('FONTSIZE', (0, 1), (-1, -1), 8)
            ]))

            story.append(suppliers_table)

            # Construire le PDF
            doc.build(story)
            return True

        except Exception as e:
            self.logger.error(f"Erreur lors de la génération du PDF des fournisseurs: {e}")
            return False

    def _generate_suppliers_excel(self, data: List[Dict[str, Any]], filepath: str) -> bool:
        """Générer un rapport Excel des fournisseurs"""
        try:
            df = pd.DataFrame(data)

            # Sélectionner les colonnes importantes
            important_columns = [
                'name', 'contact_person', 'email', 'phone', 'address', 'city',
                'articles_count', 'orders_count', 'total_orders_value', 'rating'
            ]

            available_columns = [col for col in important_columns if col in df.columns]
            df_filtered = df[available_columns]

            # Renommer les colonnes
            columns_mapping = {
                'name': 'Nom',
                'contact_person': 'Contact',
                'email': 'Email',
                'phone': 'Téléphone',
                'address': 'Adresse',
                'city': 'Ville',
                'articles_count': 'Nb Articles',
                'orders_count': 'Nb Commandes',
                'total_orders_value': 'Valeur Commandes',
                'rating': 'Note'
            }

            df_filtered = df_filtered.rename(columns=columns_mapping)

            # Écrire dans Excel
            df_filtered.to_excel(filepath, index=False)
            return True

        except Exception as e:
            self.logger.error(f"Erreur lors de la génération du Excel des fournisseurs: {e}")
            return False
