"""
Gestionnaire de base de données pour GSlim
"""

import sqlite3
import bcrypt
from pathlib import Path
from typing import Dict, List, Optional, Any
from contextlib import contextmanager

from config.settings import config
from utils.logger import setup_logger

class DatabaseManager:
    """Gestionnaire de base de données SQLite"""
    
    def __init__(self):
        """Initialiser le gestionnaire de base de données"""
        self.db_path = config.DATABASE_PATH
        self.logger = setup_logger(__name__)
        
    def initialize_database(self):
        """Initialiser la base de données avec les tables nécessaires"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Créer les tables
                self._create_users_table(cursor)
                self._create_categories_table(cursor)
                self._create_suppliers_table(cursor)
                self._create_articles_table(cursor)
                self._create_stock_movements_table(cursor)
                self._create_orders_table(cursor)
                self._create_order_items_table(cursor)
                self._create_settings_table(cursor)
                
                # Créer l'utilisateur administrateur par défaut
                self._create_default_admin(cursor)
                
                conn.commit()
                self.logger.info("Base de données initialisée avec succès")
                
        except Exception as e:
            self.logger.error(f"Erreur lors de l'initialisation de la base de données: {e}")
            raise
    
    @contextmanager
    def get_connection(self):
        """Gestionnaire de contexte pour les connexions à la base de données"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # Pour accéder aux colonnes par nom
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            raise e
        finally:
            if conn:
                conn.close()
    
    def _create_users_table(self, cursor):
        """Créer la table des utilisateurs"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                role VARCHAR(20) NOT NULL DEFAULT 'user',
                is_active BOOLEAN NOT NULL DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
    
    def _create_categories_table(self, cursor):
        """Créer la table des catégories"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL UNIQUE,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
    
    def _create_suppliers_table(self, cursor):
        """Créer la table des fournisseurs"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(200) NOT NULL,
                company_name VARCHAR(200),
                contact_person VARCHAR(100),
                email VARCHAR(100),
                phone VARCHAR(20),
                mobile VARCHAR(20),
                fax VARCHAR(20),
                website VARCHAR(200),
                address TEXT,
                city VARCHAR(100),
                postal_code VARCHAR(20),
                country VARCHAR(100) DEFAULT 'France',
                tax_number VARCHAR(50),
                payment_terms INTEGER DEFAULT 30,
                payment_method VARCHAR(50) DEFAULT 'Virement',
                discount_rate DECIMAL(5,2) DEFAULT 0,
                credit_limit DECIMAL(10,2) DEFAULT 0,
                currency VARCHAR(10) DEFAULT 'EUR',
                is_active BOOLEAN NOT NULL DEFAULT 1,
                rating INTEGER DEFAULT 0,
                notes TEXT,
                delivery_time INTEGER DEFAULT 7,
                minimum_order DECIMAL(10,2) DEFAULT 0,
                bank_details TEXT,
                category VARCHAR(50) DEFAULT 'Général',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
    
    def _create_articles_table(self, cursor):
        """Créer la table des articles"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS articles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                reference VARCHAR(50) UNIQUE NOT NULL,
                name VARCHAR(200) NOT NULL,
                description TEXT,
                category_id INTEGER,
                category_name VARCHAR(100),
                supplier_id INTEGER,
                supplier_name VARCHAR(200),
                unit_price DECIMAL(10,2) NOT NULL DEFAULT 0,
                quantity_in_stock INTEGER NOT NULL DEFAULT 0,
                minimum_stock INTEGER NOT NULL DEFAULT 0,
                maximum_stock INTEGER,
                unit VARCHAR(20) DEFAULT 'pièce',
                barcode VARCHAR(100),
                location VARCHAR(100),
                is_active BOOLEAN NOT NULL DEFAULT 1,
                tax_rate DECIMAL(5,2) DEFAULT 0,
                cost_price DECIMAL(10,2) DEFAULT 0,
                margin_percentage DECIMAL(5,2) DEFAULT 0,
                weight DECIMAL(8,3),
                dimensions VARCHAR(100),
                image_path VARCHAR(500),
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (category_id) REFERENCES categories (id),
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
            )
        """)
    
    def _create_stock_movements_table(self, cursor):
        """Créer la table des mouvements de stock"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS stock_movements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                article_id INTEGER NOT NULL,
                movement_type VARCHAR(10) NOT NULL, -- 'IN' ou 'OUT'
                quantity INTEGER NOT NULL,
                unit_price DECIMAL(10,2),
                total_price DECIMAL(10,2),
                reason VARCHAR(100),
                reference_document VARCHAR(100),
                user_id INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (article_id) REFERENCES articles (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)
    
    def _create_orders_table(self, cursor):
        """Créer la table des commandes"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_number VARCHAR(50) UNIQUE NOT NULL,
                supplier_id INTEGER NOT NULL,
                status VARCHAR(20) NOT NULL DEFAULT 'pending', -- 'pending', 'sent', 'received', 'cancelled'
                order_date DATE NOT NULL,
                expected_date DATE,
                received_date DATE,
                total_amount DECIMAL(10,2) DEFAULT 0,
                notes TEXT,
                user_id INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)
    
    def _create_order_items_table(self, cursor):
        """Créer la table des éléments de commande"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS order_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id INTEGER NOT NULL,
                article_id INTEGER NOT NULL,
                quantity INTEGER NOT NULL,
                unit_price DECIMAL(10,2) NOT NULL,
                total_price DECIMAL(10,2) NOT NULL,
                received_quantity INTEGER DEFAULT 0,
                FOREIGN KEY (order_id) REFERENCES orders (id) ON DELETE CASCADE,
                FOREIGN KEY (article_id) REFERENCES articles (id)
            )
        """)
    
    def _create_settings_table(self, cursor):
        """Créer la table des paramètres"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS settings (
                key VARCHAR(100) PRIMARY KEY,
                value TEXT,
                description TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
    
    def _create_default_admin(self, cursor):
        """Créer l'utilisateur administrateur par défaut"""
        # Vérifier si l'admin existe déjà
        cursor.execute("SELECT id FROM users WHERE username = 'admin'")
        if cursor.fetchone():
            return
        
        # Créer le mot de passe haché
        password = "admin123"  # Mot de passe par défaut
        password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        
        # Insérer l'utilisateur admin
        cursor.execute("""
            INSERT INTO users (username, email, password_hash, role)
            VALUES (?, ?, ?, ?)
        """, ("admin", "<EMAIL>", password_hash, "admin"))
        
        self.logger.info("Utilisateur administrateur créé (admin/admin123)")
    
    def authenticate_user(self, username: str, password: str) -> Optional[Dict]:
        """
        Authentifier un utilisateur
        
        Args:
            username: Nom d'utilisateur
            password: Mot de passe
            
        Returns:
            Dictionnaire avec les informations utilisateur ou None
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT id, username, email, password_hash, role, is_active
                    FROM users 
                    WHERE username = ? AND is_active = 1
                """, (username,))
                
                user = cursor.fetchone()
                if not user:
                    return None
                
                # Vérifier le mot de passe
                if bcrypt.checkpw(password.encode('utf-8'), user['password_hash'].encode('utf-8')):
                    return {
                        'id': user['id'],
                        'username': user['username'],
                        'email': user['email'],
                        'role': user['role']
                    }
                
                return None
                
        except Exception as e:
            self.logger.error(f"Erreur lors de l'authentification: {e}")
            return None
    
    def close(self):
        """Fermer les connexions à la base de données"""
        # Rien à faire pour SQLite, les connexions sont fermées automatiquement
        pass
