"""
Fenêtre de gestion des mouvements de stock
Interface pour visualiser et gérer les entrées/sorties de stock
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QTableWidget, QTableWidgetItem, QLineEdit, QComboBox,
    QFrame, QSplitter, QHeaderView, QAbstractItemView,
    QMessageBox, QDialog, QFormLayout, QSpinBox, QDoubleSpinBox,
    QTextEdit, QDateEdit, QGroupBox, QGridLayout, QTabWidget
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QDate
from PyQt5.QtGui import QFont, QColor, QPalette

try:
    from qfluentwidgets import (
        PushButton, LineEdit, ComboBox, TableWidget,
        SearchLineEdit, CardWidget, TitleLabel, CaptionLabel,
        FluentIcon, InfoBar, InfoBarPosition, DateEdit
    )
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False

from controllers.stock_movement_controller import StockMovementController
from controllers.article_controller import ArticleController
from controllers.supplier_controller import SupplierController
from models.stock_movement import get_movement_types, get_movement_reasons
from utils.logger import setup_logger
from datetime import datetime, date, timedelta


class MovementsWindow(QWidget):
    """Fenêtre de gestion des mouvements de stock"""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.logger = setup_logger(__name__)
        
        # Contrôleurs
        self.controller = StockMovementController(db_manager)
        self.article_controller = ArticleController(db_manager)
        self.supplier_controller = SupplierController(db_manager)
        
        # Variables
        self.movements_data = []
        self.filtered_data = []
        self.selected_movement = None
        
        # Timer pour actualisation automatique
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self._load_movements)
        self.refresh_timer.start(60000)  # Actualiser chaque minute
        
        self._init_ui()
        self._load_movements()
        
        self.logger.info("MovementsWindow initialisé")
    
    def _init_ui(self):
        """Initialiser l'interface utilisateur"""
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # En-tête
        self._create_header(layout)
        
        # Onglets
        self.tab_widget = QTabWidget()
        
        # Onglet Liste des mouvements
        movements_tab = QWidget()
        self._create_movements_tab(movements_tab)
        self.tab_widget.addTab(movements_tab, "Mouvements")
        
        # Onglet Nouveau mouvement
        new_movement_tab = QWidget()
        self._create_new_movement_tab(new_movement_tab)
        self.tab_widget.addTab(new_movement_tab, "Nouveau Mouvement")
        
        # Onglet Statistiques
        stats_tab = QWidget()
        self._create_stats_tab(stats_tab)
        self.tab_widget.addTab(stats_tab, "Statistiques")
        
        layout.addWidget(self.tab_widget)
        self.setLayout(layout)
    
    def _create_header(self, layout):
        """Créer l'en-tête"""
        header_layout = QHBoxLayout()
        
        if FLUENT_AVAILABLE:
            title = TitleLabel("Gestion des Mouvements de Stock")
        else:
            title = QLabel("Gestion des Mouvements de Stock")
            title.setProperty("class", "title")
            font = QFont("Segoe UI", 24, QFont.Bold)
            title.setFont(font)
        
        header_layout.addWidget(title)
        header_layout.addStretch()
        
        # Statistiques rapides
        self._create_stats_cards(header_layout)
        
        layout.addLayout(header_layout)
    
    def _create_stats_cards(self, layout):
        """Créer les cartes de statistiques"""
        try:
            stats = self.controller.get_movement_statistics()
            
            # Carte total mouvements
            total_card = self._create_stat_card("Total", str(stats.get('total_movements', 0)), "#0078d4")
            layout.addWidget(total_card)
            
            # Carte entrées
            entries_card = self._create_stat_card("Entrées", str(stats.get('total_entries', 0)), "#28a745")
            layout.addWidget(entries_card)
            
            # Carte sorties
            exits_card = self._create_stat_card("Sorties", str(stats.get('total_exits', 0)), "#dc3545")
            layout.addWidget(exits_card)
            
            # Carte aujourd'hui
            today_card = self._create_stat_card("Aujourd'hui", str(stats.get('today_movements', 0)), "#fd7e14")
            layout.addWidget(today_card)
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la création des cartes de stats: {e}")
    
    def _create_stat_card(self, title: str, value: str, color: str):
        """Créer une carte de statistique"""
        if FLUENT_AVAILABLE:
            card = CardWidget()
        else:
            card = QFrame()
            card.setProperty("class", "stat-card")
            card.setFrameStyle(QFrame.Box)
        
        card_layout = QVBoxLayout()
        card_layout.setContentsMargins(10, 10, 10, 10)
        card_layout.setSpacing(5)
        
        # Titre
        if FLUENT_AVAILABLE:
            title_label = CaptionLabel(title)
        else:
            title_label = QLabel(title)
            title_label.setProperty("class", "stat-title")
        
        title_label.setAlignment(Qt.AlignCenter)
        card_layout.addWidget(title_label)
        
        # Valeur
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setProperty("class", "stat-value")
        
        font = QFont("Segoe UI", 14, QFont.Bold)
        value_label.setFont(font)
        value_label.setStyleSheet(f"color: {color};")
        
        card_layout.addWidget(value_label)
        
        card.setLayout(card_layout)
        card.setFixedSize(100, 70)
        
        return card
    
    def _create_movements_tab(self, tab_widget):
        """Créer l'onglet de liste des mouvements"""
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)
        
        # Filtres
        self._create_filters(layout)
        
        # Table des mouvements
        self._create_movements_table(layout)
        
        # Boutons d'action
        self._create_action_buttons(layout)
        
        tab_widget.setLayout(layout)
    
    def _create_filters(self, layout):
        """Créer les filtres de recherche"""
        filters_frame = QFrame()
        filters_frame.setProperty("class", "filters-frame")
        
        filters_layout = QGridLayout()
        filters_layout.setSpacing(10)
        
        # Recherche par article
        filters_layout.addWidget(QLabel("Article:"), 0, 0)
        if FLUENT_AVAILABLE:
            self.article_filter = SearchLineEdit()
            self.article_filter.setPlaceholderText("Rechercher un article...")
        else:
            self.article_filter = QLineEdit()
            self.article_filter.setPlaceholderText("Rechercher un article...")
        
        self.article_filter.textChanged.connect(self._apply_filters)
        filters_layout.addWidget(self.article_filter, 0, 1)
        
        # Filtre par type
        filters_layout.addWidget(QLabel("Type:"), 0, 2)
        if FLUENT_AVAILABLE:
            self.type_filter = ComboBox()
        else:
            self.type_filter = QComboBox()
        
        self.type_filter.addItem("Tous les types", "")
        for movement_type in get_movement_types():
            self.type_filter.addItem(movement_type, movement_type)
        
        self.type_filter.currentTextChanged.connect(self._apply_filters)
        filters_layout.addWidget(self.type_filter, 0, 3)
        
        # Filtre par date de début
        filters_layout.addWidget(QLabel("Du:"), 1, 0)
        if FLUENT_AVAILABLE:
            self.start_date_filter = DateEdit()
        else:
            self.start_date_filter = QDateEdit()
        
        self.start_date_filter.setDate(QDate.currentDate().addDays(-30))
        self.start_date_filter.dateChanged.connect(self._apply_filters)
        filters_layout.addWidget(self.start_date_filter, 1, 1)
        
        # Filtre par date de fin
        filters_layout.addWidget(QLabel("Au:"), 1, 2)
        if FLUENT_AVAILABLE:
            self.end_date_filter = DateEdit()
        else:
            self.end_date_filter = QDateEdit()
        
        self.end_date_filter.setDate(QDate.currentDate())
        self.end_date_filter.dateChanged.connect(self._apply_filters)
        filters_layout.addWidget(self.end_date_filter, 1, 3)
        
        # Bouton de réinitialisation
        if FLUENT_AVAILABLE:
            reset_btn = PushButton("Réinitialiser")
            reset_btn.setIcon(FluentIcon.CLEAR_SELECTION)
        else:
            reset_btn = QPushButton("Réinitialiser")
        
        reset_btn.clicked.connect(self._reset_filters)
        filters_layout.addWidget(reset_btn, 1, 4)
        
        filters_frame.setLayout(filters_layout)
        layout.addWidget(filters_frame)
    
    def _create_movements_table(self, layout):
        """Créer la table des mouvements"""
        if FLUENT_AVAILABLE:
            self.movements_table = TableWidget()
        else:
            self.movements_table = QTableWidget()
        
        # Configuration de la table
        columns = [
            "Date", "Article", "Type", "Motif", "Quantité", 
            "Prix unitaire", "Valeur", "Utilisateur"
        ]
        
        self.movements_table.setColumnCount(len(columns))
        self.movements_table.setHorizontalHeaderLabels(columns)
        
        # Configuration des colonnes
        header = self.movements_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Date
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # Article
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Type
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Motif
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Quantité
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Prix
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Valeur
        
        # Configuration générale
        self.movements_table.setAlternatingRowColors(True)
        self.movements_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.movements_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.movements_table.setSortingEnabled(True)
        
        # Connecter les signaux
        self.movements_table.itemSelectionChanged.connect(self._on_movement_selected)
        self.movements_table.itemDoubleClicked.connect(self._view_movement_details)
        
        layout.addWidget(self.movements_table)
    
    def _create_action_buttons(self, layout):
        """Créer les boutons d'action"""
        buttons_layout = QHBoxLayout()
        
        if FLUENT_AVAILABLE:
            self.refresh_button = PushButton("Actualiser")
            self.refresh_button.setIcon(FluentIcon.SYNC)
            
            self.export_button = PushButton("Exporter")
            self.export_button.setIcon(FluentIcon.DOWNLOAD)
            
            self.details_button = PushButton("Détails")
            self.details_button.setIcon(FluentIcon.VIEW)
        else:
            self.refresh_button = QPushButton("Actualiser")
            self.export_button = QPushButton("Exporter")
            self.details_button = QPushButton("Détails")
        
        # Connecter les signaux
        self.refresh_button.clicked.connect(self._load_movements)
        self.export_button.clicked.connect(self._export_movements)
        self.details_button.clicked.connect(self._view_movement_details)
        
        # État initial des boutons
        self.details_button.setEnabled(False)
        
        buttons_layout.addWidget(self.refresh_button)
        buttons_layout.addWidget(self.export_button)
        buttons_layout.addWidget(self.details_button)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
    
    def _create_new_movement_tab(self, tab_widget):
        """Créer l'onglet de nouveau mouvement"""
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(20)
        
        # Formulaire de nouveau mouvement
        form_frame = QFrame()
        form_frame.setProperty("class", "form-frame")
        
        form_layout = QFormLayout()
        form_layout.setSpacing(15)
        
        # Sélection de l'article
        if FLUENT_AVAILABLE:
            self.new_article_combo = ComboBox()
        else:
            self.new_article_combo = QComboBox()
            self.new_article_combo.setEditable(True)
        form_layout.addRow("Article:", self.new_article_combo)
        
        # Type de mouvement
        if FLUENT_AVAILABLE:
            self.new_type_combo = ComboBox()
        else:
            self.new_type_combo = QComboBox()
        
        for movement_type in get_movement_types():
            self.new_type_combo.addItem(movement_type)
        
        self.new_type_combo.currentTextChanged.connect(self._on_movement_type_changed)
        form_layout.addRow("Type:", self.new_type_combo)
        
        # Motif
        if FLUENT_AVAILABLE:
            self.new_reason_combo = ComboBox()
        else:
            self.new_reason_combo = QComboBox()
            self.new_reason_combo.setEditable(True)
        form_layout.addRow("Motif:", self.new_reason_combo)
        
        # Quantité
        self.new_quantity_spin = QSpinBox()
        self.new_quantity_spin.setMinimum(1)
        self.new_quantity_spin.setMaximum(999999)
        self.new_quantity_spin.setValue(1)
        form_layout.addRow("Quantité:", self.new_quantity_spin)
        
        # Prix unitaire
        self.new_price_spin = QDoubleSpinBox()
        self.new_price_spin.setMinimum(0.0)
        self.new_price_spin.setMaximum(999999.99)
        self.new_price_spin.setDecimals(2)
        self.new_price_spin.setSuffix(" €")
        form_layout.addRow("Prix unitaire:", self.new_price_spin)
        
        # Fournisseur (pour les entrées)
        if FLUENT_AVAILABLE:
            self.new_supplier_combo = ComboBox()
        else:
            self.new_supplier_combo = QComboBox()
        
        self.new_supplier_combo.addItem("Aucun", None)
        form_layout.addRow("Fournisseur:", self.new_supplier_combo)
        
        # Notes
        self.new_notes_text = QTextEdit()
        self.new_notes_text.setMaximumHeight(80)
        form_layout.addRow("Notes:", self.new_notes_text)
        
        form_frame.setLayout(form_layout)
        layout.addWidget(form_frame)
        
        # Boutons
        buttons_layout = QHBoxLayout()
        
        if FLUENT_AVAILABLE:
            self.create_movement_btn = PushButton("Créer le mouvement")
            self.create_movement_btn.setIcon(FluentIcon.ADD)
            
            self.clear_form_btn = PushButton("Effacer")
            self.clear_form_btn.setIcon(FluentIcon.CLEAR_SELECTION)
        else:
            self.create_movement_btn = QPushButton("Créer le mouvement")
            self.clear_form_btn = QPushButton("Effacer")
        
        self.create_movement_btn.clicked.connect(self._create_movement)
        self.clear_form_btn.clicked.connect(self._clear_movement_form)
        
        buttons_layout.addWidget(self.create_movement_btn)
        buttons_layout.addWidget(self.clear_form_btn)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
        layout.addStretch()
        
        tab_widget.setLayout(layout)
        
        # Charger les données pour les combos
        self._load_form_data()
    
    def _create_stats_tab(self, tab_widget):
        """Créer l'onglet des statistiques"""
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(20)
        
        # Statistiques détaillées
        stats_frame = QFrame()
        stats_frame.setProperty("class", "stats-frame")
        
        stats_layout = QGridLayout()
        
        # Charger les statistiques
        try:
            stats = self.controller.get_movement_statistics()
            
            # Créer les widgets de statistiques
            stats_widgets = [
                ("Total mouvements", str(stats.get('total_movements', 0)), "#0078d4"),
                ("Entrées", str(stats.get('total_entries', 0)), "#28a745"),
                ("Sorties", str(stats.get('total_exits', 0)), "#dc3545"),
                ("Ajustements", str(stats.get('total_adjustments', 0)), "#6f42c1"),
                ("Valeur entrées", f"{stats.get('entry_value', 0):.2f} €", "#28a745"),
                ("Valeur sorties", f"{stats.get('exit_value', 0):.2f} €", "#dc3545"),
                ("Mouvements aujourd'hui", str(stats.get('today_movements', 0)), "#fd7e14"),
                ("Mouvements cette semaine", str(stats.get('week_movements', 0)), "#17a2b8")
            ]
            
            for i, (title, value, color) in enumerate(stats_widgets):
                card = self._create_stat_card(title, value, color)
                card.setFixedSize(150, 100)
                stats_layout.addWidget(card, i // 4, i % 4)
            
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des statistiques: {e}")
        
        stats_frame.setLayout(stats_layout)
        layout.addWidget(stats_frame)
        
        layout.addStretch()
        tab_widget.setLayout(layout)
    
    def _load_movements(self):
        """Charger la liste des mouvements"""
        try:
            # Récupérer les mouvements récents (par défaut 30 derniers jours)
            self.movements_data = self.controller.get_recent_movements(1000)
            self._apply_filters()
            
            # Mettre à jour les statistiques
            self._update_stats_cards()
            
            self.logger.info(f"{len(self.movements_data)} mouvements chargés")
            
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des mouvements: {e}")
            self._show_error("Erreur", f"Impossible de charger les mouvements: {e}")
    
    def _apply_filters(self):
        """Appliquer les filtres à la liste des mouvements"""
        try:
            self.filtered_data = self.movements_data.copy()
            
            # Filtre par article
            article_filter = self.article_filter.text().lower()
            if article_filter:
                self.filtered_data = [
                    m for m in self.filtered_data 
                    if article_filter in m.get('article_name', '').lower()
                ]
            
            # Filtre par type
            type_filter = self.type_filter.currentData()
            if type_filter:
                self.filtered_data = [
                    m for m in self.filtered_data 
                    if m.get('movement_type') == type_filter
                ]
            
            # Filtre par date
            start_date = self.start_date_filter.date().toPyDate()
            end_date = self.end_date_filter.date().toPyDate()
            
            self.filtered_data = [
                m for m in self.filtered_data
                if self._is_movement_in_date_range(m, start_date, end_date)
            ]
            
            # Mettre à jour la table
            self._update_movements_table()
            
        except Exception as e:
            self.logger.error(f"Erreur lors de l'application des filtres: {e}")
    
    def _is_movement_in_date_range(self, movement: dict, start_date: date, end_date: date) -> bool:
        """Vérifier si un mouvement est dans la plage de dates"""
        try:
            movement_date_str = movement.get('movement_date', '')
            if not movement_date_str:
                return False
            
            # Parser la date du mouvement
            movement_date = datetime.fromisoformat(movement_date_str.replace('Z', '+00:00')).date()
            
            return start_date <= movement_date <= end_date
            
        except Exception:
            return False

    def _update_movements_table(self):
        """Mettre à jour la table des mouvements"""
        try:
            self.movements_table.setRowCount(len(self.filtered_data))

            for row, movement in enumerate(self.filtered_data):
                # Date
                movement_date = movement.get('movement_date', '')
                if movement_date:
                    try:
                        date_obj = datetime.fromisoformat(movement_date.replace('Z', '+00:00'))
                        formatted_date = date_obj.strftime('%d/%m/%Y %H:%M')
                    except:
                        formatted_date = movement_date[:16]
                else:
                    formatted_date = ''

                self.movements_table.setItem(row, 0, QTableWidgetItem(formatted_date))
                self.movements_table.setItem(row, 1, QTableWidgetItem(movement.get('article_name', '')))
                self.movements_table.setItem(row, 2, QTableWidgetItem(movement.get('movement_type', '')))
                self.movements_table.setItem(row, 3, QTableWidgetItem(movement.get('movement_reason', '')))
                self.movements_table.setItem(row, 4, QTableWidgetItem(str(movement.get('quantity', 0))))
                self.movements_table.setItem(row, 5, QTableWidgetItem(f"{movement.get('unit_price', 0):.2f} €"))
                self.movements_table.setItem(row, 6, QTableWidgetItem(f"{movement.get('total_value', 0):.2f} €"))
                self.movements_table.setItem(row, 7, QTableWidgetItem(movement.get('user_name', '')))

                # Colorer les lignes selon le type
                movement_type = movement.get('movement_type', '')
                if movement_type == 'Entrée':
                    color = QColor(200, 255, 200)  # Vert clair
                elif movement_type == 'Sortie':
                    color = QColor(255, 200, 200)  # Rouge clair
                else:
                    color = QColor(255, 255, 200)  # Jaune clair

                for col in range(self.movements_table.columnCount()):
                    item = self.movements_table.item(row, col)
                    if item:
                        item.setBackground(color)

        except Exception as e:
            self.logger.error(f"Erreur lors de la mise à jour de la table: {e}")

    def _update_stats_cards(self):
        """Mettre à jour les cartes de statistiques"""
        try:
            stats = self.controller.get_movement_statistics()
            # Les cartes sont créées dans _create_stats_cards,
            # ici on pourrait les mettre à jour si nécessaire
        except Exception as e:
            self.logger.error(f"Erreur lors de la mise à jour des stats: {e}")

    def _reset_filters(self):
        """Réinitialiser tous les filtres"""
        self.article_filter.clear()
        self.type_filter.setCurrentIndex(0)
        self.start_date_filter.setDate(QDate.currentDate().addDays(-30))
        self.end_date_filter.setDate(QDate.currentDate())
        self._apply_filters()

    def _on_movement_selected(self):
        """Gérer la sélection d'un mouvement"""
        selected_rows = self.movements_table.selectionModel().selectedRows()
        if selected_rows:
            row = selected_rows[0].row()
            if 0 <= row < len(self.filtered_data):
                self.selected_movement = self.filtered_data[row]
                self.details_button.setEnabled(True)
            else:
                self.selected_movement = None
                self.details_button.setEnabled(False)
        else:
            self.selected_movement = None
            self.details_button.setEnabled(False)

    def _view_movement_details(self):
        """Afficher les détails d'un mouvement"""
        if not self.selected_movement:
            return

        dialog = MovementDetailsDialog(self.selected_movement, self)
        dialog.exec_()

    def _export_movements(self):
        """Exporter les mouvements filtrés"""
        try:
            if not self.filtered_data:
                self._show_warning("Aucune donnée", "Aucun mouvement à exporter")
                return

            # Utiliser le contrôleur de rapports pour exporter
            from controllers.report_controller import ReportController
            report_controller = ReportController(self.db_manager)

            # Obtenir les dates de filtre
            start_date = self.start_date_filter.date().toPyDate()
            end_date = self.end_date_filter.date().toPyDate()

            # Générer le rapport
            result = report_controller.generate_movements_report(start_date, end_date, "excel")

            if result.success:
                self._show_success("Export réussi", f"Fichier exporté: {result.data}")
            else:
                self._show_error("Erreur d'export", result.message)

        except Exception as e:
            self.logger.error(f"Erreur lors de l'export: {e}")
            self._show_error("Erreur", f"Erreur lors de l'export: {e}")

    def _load_form_data(self):
        """Charger les données pour le formulaire"""
        try:
            # Charger les articles
            articles = self.article_controller.get_all()
            self.new_article_combo.clear()
            for article in articles:
                self.new_article_combo.addItem(
                    f"{article['name']} ({article['reference']})",
                    article['id']
                )

            # Charger les fournisseurs
            suppliers = self.supplier_controller.get_all()
            self.new_supplier_combo.clear()
            self.new_supplier_combo.addItem("Aucun", None)
            for supplier in suppliers:
                self.new_supplier_combo.addItem(supplier['name'], supplier['id'])

            # Mettre à jour les motifs selon le type
            self._on_movement_type_changed()

        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des données du formulaire: {e}")

    def _on_movement_type_changed(self):
        """Gérer le changement de type de mouvement"""
        movement_type = self.new_type_combo.currentText()

        # Mettre à jour les motifs disponibles
        self.new_reason_combo.clear()

        if movement_type == "Entrée":
            reasons = ["Réception", "Retour client", "Ajustement positif", "Autre"]
            self.new_supplier_combo.setEnabled(True)
        elif movement_type == "Sortie":
            reasons = ["Vente", "Perte", "Casse", "Ajustement négatif", "Autre"]
            self.new_supplier_combo.setEnabled(False)
        else:  # Ajustement
            reasons = ["Inventaire", "Correction", "Autre"]
            self.new_supplier_combo.setEnabled(False)

        for reason in reasons:
            self.new_reason_combo.addItem(reason)

    def _create_movement(self):
        """Créer un nouveau mouvement"""
        try:
            # Validation des données
            if self.new_article_combo.currentData() is None:
                self._show_warning("Validation", "Veuillez sélectionner un article")
                return

            if self.new_quantity_spin.value() <= 0:
                self._show_warning("Validation", "La quantité doit être positive")
                return

            # Récupérer les données du formulaire
            article_id = self.new_article_combo.currentData()
            movement_type = self.new_type_combo.currentText()
            quantity = self.new_quantity_spin.value()
            unit_price = self.new_price_spin.value()
            reason = self.new_reason_combo.currentText()
            supplier_id = self.new_supplier_combo.currentData()
            notes = self.new_notes_text.toPlainText()

            # Créer le mouvement selon le type
            if movement_type == "Entrée":
                result = self.controller.create_entry_movement(
                    article_id=article_id,
                    quantity=quantity,
                    unit_price=unit_price,
                    reason=reason,
                    supplier_id=supplier_id,
                    notes=notes
                )
            elif movement_type == "Sortie":
                result = self.controller.create_exit_movement(
                    article_id=article_id,
                    quantity=quantity,
                    reason=reason,
                    notes=notes
                )
            else:
                self._show_warning("Non implémenté", "Les ajustements manuels ne sont pas encore implémentés")
                return

            if result.success:
                self._show_success("Succès", result.message)
                self._clear_movement_form()
                self._load_movements()  # Recharger la liste
                self.tab_widget.setCurrentIndex(0)  # Retourner à l'onglet liste
            else:
                self._show_error("Erreur", result.message)

        except Exception as e:
            self.logger.error(f"Erreur lors de la création du mouvement: {e}")
            self._show_error("Erreur", f"Erreur lors de la création: {e}")

    def _clear_movement_form(self):
        """Effacer le formulaire de nouveau mouvement"""
        self.new_article_combo.setCurrentIndex(0)
        self.new_type_combo.setCurrentIndex(0)
        self.new_reason_combo.setCurrentIndex(0)
        self.new_quantity_spin.setValue(1)
        self.new_price_spin.setValue(0.0)
        self.new_supplier_combo.setCurrentIndex(0)
        self.new_notes_text.clear()

    def _show_success(self, title: str, message: str):
        """Afficher un message de succès"""
        if FLUENT_AVAILABLE:
            InfoBar.success(title, message, parent=self)
        else:
            QMessageBox.information(self, title, message)

    def _show_warning(self, title: str, message: str):
        """Afficher un message d'avertissement"""
        if FLUENT_AVAILABLE:
            InfoBar.warning(title, message, parent=self)
        else:
            QMessageBox.warning(self, title, message)

    def _show_error(self, title: str, message: str):
        """Afficher un message d'erreur"""
        if FLUENT_AVAILABLE:
            InfoBar.error(title, message, parent=self)
        else:
            QMessageBox.critical(self, title, message)


class MovementDetailsDialog(QDialog):
    """Dialog pour afficher les détails d'un mouvement"""

    def __init__(self, movement_data: dict, parent=None):
        super().__init__(parent)
        self.movement_data = movement_data
        self.setWindowTitle("Détails du Mouvement")
        self.setModal(True)
        self.resize(500, 400)

        self._init_ui()

    def _init_ui(self):
        """Initialiser l'interface du dialog"""
        layout = QVBoxLayout()

        # Titre
        title = QLabel("Détails du Mouvement")
        title.setProperty("class", "dialog-title")
        font = QFont("Segoe UI", 16, QFont.Bold)
        title.setFont(font)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # Informations détaillées
        details_frame = QFrame()
        details_layout = QFormLayout()

        # Ajouter tous les champs disponibles
        fields = [
            ("ID", "id"),
            ("Date", "movement_date"),
            ("Article", "article_name"),
            ("Référence", "article_reference"),
            ("Type", "movement_type"),
            ("Motif", "movement_reason"),
            ("Quantité", "quantity"),
            ("Prix unitaire", "unit_price"),
            ("Valeur totale", "total_value"),
            ("Stock précédent", "previous_quantity"),
            ("Nouveau stock", "new_quantity"),
            ("Fournisseur", "supplier_name"),
            ("Utilisateur", "user_name"),
            ("Notes", "notes")
        ]

        for label, key in fields:
            value = self.movement_data.get(key, '')

            # Formatage spécial pour certains champs
            if key in ['unit_price', 'total_value'] and value:
                value = f"{float(value):.2f} €"
            elif key == 'movement_date' and value:
                try:
                    date_obj = datetime.fromisoformat(value.replace('Z', '+00:00'))
                    value = date_obj.strftime('%d/%m/%Y à %H:%M:%S')
                except:
                    pass

            value_label = QLabel(str(value) if value else "Non renseigné")
            details_layout.addRow(f"{label}:", value_label)

        details_frame.setLayout(details_layout)
        layout.addWidget(details_frame)

        # Bouton de fermeture
        close_button = QPushButton("Fermer")
        close_button.clicked.connect(self.accept)
        layout.addWidget(close_button)

        self.setLayout(layout)
