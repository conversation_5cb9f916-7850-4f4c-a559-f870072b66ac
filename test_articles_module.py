#!/usr/bin/env python3
"""
Test du module de gestion des articles
"""

import sys
from pathlib import Path

# Ajouter le répertoire src au path Python
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_models():
    """Tester les modèles"""
    print("🧪 Test des modèles...")
    
    try:
        from models.article import Article
        from models.supplier import Supplier
        from models.base import ValidationError
        
        # Test Article
        article_data = {
            'name': 'Test Article',
            'reference': 'TEST001',
            'unit_price': 10.50,
            'quantity_in_stock': 100,
            'minimum_stock': 10
        }
        
        article = Article(**article_data)
        if article.validate():
            print("  ✅ Modèle Article validé")
            print(f"  ✅ Valeur totale: {article.get_total_value()}")
            print(f"  ✅ Statut stock: {article.get_stock_status()}")
        else:
            print(f"  ❌ Erreurs Article: {article.get_errors()}")
        
        # Test Supplier
        supplier_data = {
            'name': 'Test Supplier',
            'email': '<EMAIL>',
            'phone': '0123456789'
        }
        
        supplier = Supplier(**supplier_data)
        if supplier.validate():
            print("  ✅ Modèle Supplier validé")
            print(f"  ✅ Nom complet: {supplier.get_full_name()}")
            print(f"  ✅ Contact: {supplier.get_primary_contact()}")
        else:
            print(f"  ❌ Erreurs Supplier: {supplier.get_errors()}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur modèles: {e}")
        return False

def test_controllers():
    """Tester les contrôleurs"""
    print("\n🎮 Test des contrôleurs...")
    
    try:
        from database.manager import DatabaseManager
        from controllers.article_controller import ArticleController
        from controllers.supplier_controller import SupplierController
        
        # Initialiser la base de données
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        # Test ArticleController
        article_controller = ArticleController(db_manager)
        
        # Créer un article de test
        article_data = {
            'name': 'Article Test Controller',
            'reference': 'CTRL001',
            'unit_price': 15.99,
            'quantity_in_stock': 50,
            'minimum_stock': 5
        }
        
        result = article_controller.create_article(article_data)
        if result.success:
            print("  ✅ Article créé via contrôleur")
            article_id = result.data['id']
            
            # Test récupération
            article = article_controller.get_article_with_details(article_id)
            if article:
                print(f"  ✅ Article récupéré: {article['name']}")
                print(f"  ✅ Statut: {article['stock_status']}")
            
            # Test mise à jour stock
            stock_result = article_controller.update_stock(article_id, 25, "Test")
            if stock_result.success:
                print("  ✅ Stock mis à jour")
            
            # Test statistiques
            stats = article_controller.get_stock_statistics()
            print(f"  ✅ Statistiques: {stats['total_articles']} articles")
            
        else:
            print(f"  ❌ Erreur création article: {result.message}")
        
        # Test SupplierController
        supplier_controller = SupplierController(db_manager)
        
        supplier_data = {
            'name': 'Fournisseur Test',
            'email': '<EMAIL>',
            'phone': '0987654321'
        }
        
        supplier_result = supplier_controller.create_supplier(supplier_data)
        if supplier_result.success:
            print("  ✅ Fournisseur créé via contrôleur")
        else:
            print(f"  ❌ Erreur création fournisseur: {supplier_result.message}")
        
        db_manager.close()
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur contrôleurs: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_interface():
    """Tester l'interface articles"""
    print("\n🖼️ Test de l'interface articles...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from views.articles_window import ArticlesWindow
        
        # Mock de l'instance app
        class MockApp:
            def get_database_manager(self):
                from database.manager import DatabaseManager
                db = DatabaseManager()
                db.initialize_database()
                return db
        
        app = QApplication([])
        mock_app = MockApp()
        
        # Créer la fenêtre articles
        articles_window = ArticlesWindow(mock_app)
        print("  ✅ Fenêtre articles créée")
        
        # Tester le chargement
        articles_window._load_articles()
        print(f"  ✅ Articles chargés: {len(articles_window.current_articles)}")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur interface: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale de test"""
    print("🧪 Tests du module Articles GSlim")
    print("=" * 50)
    
    tests = [
        ("Modèles", test_models),
        ("Contrôleurs", test_controllers),
        ("Interface", test_interface)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur dans {test_name}: {e}")
            results.append((test_name, False))
    
    # Résumé
    print("\n" + "=" * 50)
    print("📊 Résumé des tests:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHEC"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Résultat: {passed}/{len(tests)} tests réussis")
    
    if passed == len(tests):
        print("🎉 Module Articles prêt !")
        print("\n💡 Pour tester l'interface complète:")
        print("   python main.py")
        print("   Puis naviguez vers 'Articles' dans le menu")
    else:
        print("⚠️ Certains tests ont échoué.")
    
    return passed == len(tests)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
