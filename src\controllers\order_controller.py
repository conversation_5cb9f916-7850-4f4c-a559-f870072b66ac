"""
Contrôleur pour la gestion des commandes fournisseurs
Gère la création, suivi et réception des commandes
"""

from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, date
from decimal import Decimal

from .base import BaseController, CRUDResult
from utils.logger import setup_logger


class OrderController(BaseController):
    """Contrôleur pour les commandes fournisseurs"""

    def __init__(self, db_manager):
        super().__init__(db_manager)
        self.logger = setup_logger(__name__)
        self.logger.info("OrderController initialisé")

    def get_table_name(self):
        """Retourner le nom de la table"""
        return 'orders'
    
    def create_order(self, order_data: Dict[str, Any], items: List[Dict[str, Any]]) -> CRUDResult:
        """Créer une nouvelle commande avec ses articles"""
        try:
            # Validation des données
            validation_result = self._validate_order_data(order_data, items)
            if not validation_result.success:
                return validation_result
            
            cursor = self.db_manager.cursor()

            # Générer un numéro de commande unique
            order_number = self._generate_order_number()
            
            # Calculer le montant total
            total_amount = sum(Decimal(str(item['quantity'])) * Decimal(str(item['unit_price'])) for item in items)
            
            # Insérer la commande
            insert_order_query = """
                INSERT INTO orders (
                    order_number, supplier_id, status, order_date, 
                    expected_date, total_amount, notes, user_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            order_values = (
                order_number,
                order_data['supplier_id'],
                order_data.get('status', 'pending'),
                order_data.get('order_date', datetime.now().date().isoformat()),
                order_data.get('expected_date'),
                float(total_amount),
                order_data.get('notes', ''),
                order_data.get('user_id', 1)  # TODO: Récupérer l'utilisateur actuel
            )
            
            cursor.execute(insert_order_query, order_values)
            order_id = cursor.lastrowid
            
            # Insérer les articles de la commande
            insert_item_query = """
                INSERT INTO order_items (
                    order_id, article_id, article_name, article_reference,
                    quantity, unit_price, total_price
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            
            for item in items:
                item_total = Decimal(str(item['quantity'])) * Decimal(str(item['unit_price']))
                item_values = (
                    order_id,
                    item['article_id'],
                    item['article_name'],
                    item.get('article_reference', ''),
                    item['quantity'],
                    float(item['unit_price']),
                    float(item_total)
                )
                cursor.execute(insert_item_query, item_values)
            
            self.db_manager.commit()
            
            self.logger.info(f"Commande créée: {order_number}, Fournisseur ID {order_data['supplier_id']}, {len(items)} articles")
            return CRUDResult(True, order_id, f"Commande {order_number} créée avec succès")
            
        except Exception as e:
            self.db_manager.rollback()
            self.logger.error(f"Erreur lors de la création de la commande: {e}")
            return CRUDResult(False, None, f"Erreur lors de la création: {e}")
    
    def get_order_with_items(self, order_id: int) -> Optional[Dict[str, Any]]:
        """Récupérer une commande avec ses articles"""
        try:
            cursor = self.db_manager.cursor()

            # Récupérer la commande
            order_query = """
                SELECT o.*, s.name as supplier_name, s.contact_person, s.email, s.phone
                FROM orders o
                LEFT JOIN suppliers s ON o.supplier_id = s.id
                WHERE o.id = ?
            """
            
            cursor.execute(order_query, (order_id,))
            order = cursor.fetchone()
            
            if not order:
                return None
            
            order_dict = dict(order)
            
            # Récupérer les articles de la commande
            items_query = """
                SELECT * FROM order_items 
                WHERE order_id = ?
                ORDER BY article_name
            """
            
            cursor.execute(items_query, (order_id,))
            items = cursor.fetchall()
            
            order_dict['items'] = [dict(item) for item in items]
            
            return order_dict
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération de la commande: {e}")
            return None
    
    def update_order_status(self, order_id: int, new_status: str, notes: str = "") -> CRUDResult:
        """Mettre à jour le statut d'une commande"""
        try:
            valid_statuses = ['pending', 'sent', 'received', 'cancelled']
            if new_status not in valid_statuses:
                return CRUDResult(False, None, f"Statut invalide. Statuts valides: {', '.join(valid_statuses)}")
            
            cursor = self.db_manager.cursor()

            # Préparer les champs à mettre à jour
            update_fields = ['status = ?', 'updated_at = ?']
            values = [new_status, datetime.now().isoformat()]
            
            # Ajouter la date de réception si le statut est 'received'
            if new_status == 'received':
                update_fields.append('received_date = ?')
                values.append(datetime.now().date().isoformat())
            
            # Ajouter les notes si fournies
            if notes:
                current_notes_query = "SELECT notes FROM orders WHERE id = ?"
                cursor.execute(current_notes_query, (order_id,))
                result = cursor.fetchone()
                current_notes = result['notes'] if result and result['notes'] else ""
                
                new_notes = f"{current_notes}\n{datetime.now().strftime('%Y-%m-%d %H:%M')}: {notes}".strip()
                update_fields.append('notes = ?')
                values.append(new_notes)
            
            values.append(order_id)
            
            update_query = f"UPDATE orders SET {', '.join(update_fields)} WHERE id = ?"
            cursor.execute(update_query, values)
            
            if cursor.rowcount == 0:
                return CRUDResult(False, None, "Commande non trouvée")
            
            self.db_manager.commit()
            
            # Si la commande est reçue, créer les mouvements de stock
            if new_status == 'received':
                self._process_received_order(order_id)
            
            self.logger.info(f"Statut de la commande {order_id} mis à jour: {new_status}")
            return CRUDResult(True, order_id, f"Statut mis à jour: {new_status}")
            
        except Exception as e:
            self.db_manager.rollback()
            self.logger.error(f"Erreur lors de la mise à jour du statut: {e}")
            return CRUDResult(False, None, f"Erreur lors de la mise à jour: {e}")
    
    def get_orders_by_supplier(self, supplier_id: int) -> List[Dict[str, Any]]:
        """Récupérer les commandes d'un fournisseur"""
        try:
            cursor = self.db_manager.cursor()

            query = """
                SELECT o.*, s.name as supplier_name
                FROM orders o
                LEFT JOIN suppliers s ON o.supplier_id = s.id
                WHERE o.supplier_id = ?
                ORDER BY o.order_date DESC
            """
            
            cursor.execute(query, (supplier_id,))
            orders = cursor.fetchall()
            
            return [dict(order) for order in orders]
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des commandes par fournisseur: {e}")
            return []
    
    def get_orders_by_status(self, status: str) -> List[Dict[str, Any]]:
        """Récupérer les commandes par statut"""
        try:
            cursor = self.db_manager.cursor()

            query = """
                SELECT o.*, s.name as supplier_name, s.contact_person, s.phone
                FROM orders o
                LEFT JOIN suppliers s ON o.supplier_id = s.id
                WHERE o.status = ?
                ORDER BY o.order_date DESC
            """
            
            cursor.execute(query, (status,))
            orders = cursor.fetchall()
            
            return [dict(order) for order in orders]
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des commandes par statut: {e}")
            return []
    
    def get_pending_orders(self) -> List[Dict[str, Any]]:
        """Récupérer les commandes en attente"""
        return self.get_orders_by_status('pending')
    
    def get_overdue_orders(self) -> List[Dict[str, Any]]:
        """Récupérer les commandes en retard"""
        try:
            cursor = self.db_manager.cursor()

            query = """
                SELECT o.*, s.name as supplier_name, s.contact_person, s.phone
                FROM orders o
                LEFT JOIN suppliers s ON o.supplier_id = s.id
                WHERE o.status IN ('pending', 'sent')
                AND o.expected_date < DATE('now')
                ORDER BY o.expected_date ASC
            """
            
            cursor.execute(query)
            orders = cursor.fetchall()
            
            return [dict(order) for order in orders]
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des commandes en retard: {e}")
            return []
    
    def get_order_statistics(self) -> Dict[str, Any]:
        """Obtenir des statistiques sur les commandes"""
        try:
            cursor = self.db_manager.cursor()

            query = """
                SELECT
                    COUNT(*) as total_orders,
                    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_orders,
                    COUNT(CASE WHEN status = 'sent' THEN 1 END) as sent_orders,
                    COUNT(CASE WHEN status = 'received' THEN 1 END) as received_orders,
                    COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_orders,
                    SUM(total_amount) as total_value,
                    SUM(CASE WHEN status = 'pending' THEN total_amount ELSE 0 END) as pending_value,
                    COUNT(CASE WHEN status IN ('pending', 'sent') AND expected_date < DATE('now') THEN 1 END) as overdue_orders,
                    COUNT(CASE WHEN DATE(order_date) = DATE('now') THEN 1 END) as today_orders,
                    COUNT(CASE WHEN DATE(order_date) >= DATE('now', '-7 days') THEN 1 END) as week_orders
                FROM orders
            """
            
            cursor.execute(query)
            result = cursor.fetchone()
            
            return dict(result) if result else {}
            
        except Exception as e:
            self.logger.error(f"Erreur lors du calcul des statistiques: {e}")
            return {}
    
    def _generate_order_number(self) -> str:
        """Générer un numéro de commande unique"""
        try:
            cursor = self.db_manager.cursor()

            # Format: CMD-YYYYMMDD-XXX
            today = datetime.now().strftime('%Y%m%d')
            prefix = f"CMD-{today}-"
            
            # Trouver le prochain numéro séquentiel pour aujourd'hui
            query = """
                SELECT order_number FROM orders 
                WHERE order_number LIKE ? 
                ORDER BY order_number DESC 
                LIMIT 1
            """
            
            cursor.execute(query, (f"{prefix}%",))
            result = cursor.fetchone()
            
            if result:
                last_number = result['order_number']
                sequence = int(last_number.split('-')[-1]) + 1
            else:
                sequence = 1
            
            return f"{prefix}{sequence:03d}"
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la génération du numéro de commande: {e}")
            return f"CMD-{datetime.now().strftime('%Y%m%d')}-001"
    
    def _validate_order_data(self, order_data: Dict[str, Any], items: List[Dict[str, Any]]) -> CRUDResult:
        """Valider les données d'une commande"""
        # Validation des données de base
        if 'supplier_id' not in order_data or not order_data['supplier_id']:
            return CRUDResult(False, None, "Fournisseur requis")
        
        if not items:
            return CRUDResult(False, None, "Au moins un article est requis")
        
        # Validation des articles
        for i, item in enumerate(items):
            if 'article_id' not in item or not item['article_id']:
                return CRUDResult(False, None, f"Article ID requis pour l'article {i+1}")
            
            if 'quantity' not in item or not isinstance(item['quantity'], (int, float)) or item['quantity'] <= 0:
                return CRUDResult(False, None, f"Quantité invalide pour l'article {i+1}")
            
            if 'unit_price' not in item or not isinstance(item['unit_price'], (int, float, Decimal)) or item['unit_price'] < 0:
                return CRUDResult(False, None, f"Prix unitaire invalide pour l'article {i+1}")
        
        return CRUDResult(True, None, "Validation réussie")
    
    def _process_received_order(self, order_id: int):
        """Traiter une commande reçue en créant les mouvements de stock"""
        try:
            order = self.get_order_with_items(order_id)
            if not order:
                return
            
            from .stock_movement_controller import StockMovementController
            movement_controller = StockMovementController(self.db_manager)
            
            for item in order['items']:
                # Créer un mouvement d'entrée pour chaque article
                movement_controller.create_entry_movement(
                    article_id=item['article_id'],
                    quantity=item['quantity'],
                    unit_price=Decimal(str(item['unit_price'])),
                    reason="Réception commande",
                    supplier_id=order['supplier_id'],
                    order_id=order_id,
                    notes=f"Commande {order['order_number']}"
                )
            
            self.logger.info(f"Mouvements de stock créés pour la commande {order['order_number']}")
            
        except Exception as e:
            self.logger.error(f"Erreur lors du traitement de la commande reçue: {e}")
    
    def cancel_order(self, order_id: int, reason: str = "") -> CRUDResult:
        """Annuler une commande"""
        try:
            # Vérifier que la commande peut être annulée
            order = self.get_by_id(order_id)
            if not order:
                return CRUDResult(False, None, "Commande non trouvée")
            
            if order['status'] == 'received':
                return CRUDResult(False, None, "Impossible d'annuler une commande déjà reçue")
            
            if order['status'] == 'cancelled':
                return CRUDResult(False, None, "Commande déjà annulée")
            
            # Mettre à jour le statut
            notes = f"Annulation: {reason}" if reason else "Commande annulée"
            return self.update_order_status(order_id, 'cancelled', notes)
            
        except Exception as e:
            self.logger.error(f"Erreur lors de l'annulation de la commande: {e}")
            return CRUDResult(False, None, f"Erreur lors de l'annulation: {e}")
    
    def get_recent_orders(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Récupérer les commandes récentes"""
        try:
            cursor = self.db_manager.cursor()

            query = """
                SELECT o.*, s.name as supplier_name
                FROM orders o
                LEFT JOIN suppliers s ON o.supplier_id = s.id
                ORDER BY o.created_at DESC
                LIMIT ?
            """
            
            cursor.execute(query, (limit,))
            orders = cursor.fetchall()
            
            return [dict(order) for order in orders]
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des commandes récentes: {e}")
            return []
