#!/usr/bin/env python3
"""
Script de test pour l'application GSlim
"""

import sys
import os
from pathlib import Path

# Ajouter le répertoire src au path Python
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """Tester tous les imports nécessaires"""
    print("🔍 Test des imports...")
    
    try:
        # Test PyQt5
        from PyQt5.QtWidgets import QApplication
        print("✅ PyQt5 importé avec succès")
        
        # Test configuration
        from config.settings import config
        print(f"✅ Configuration chargée: {config.APP_NAME} v{config.APP_VERSION}")
        
        # Test base de données
        from database.manager import DatabaseManager
        print("✅ Gestionnaire de base de données importé")
        
        # Test thèmes
        from styles.themes import theme_manager
        print(f"✅ Gestionnaire de thèmes importé (thème actuel: {theme_manager.current_theme.value})")
        
        # Test vues
        from views.login_window import LoginWindow
        from views.main_window import MainWindow
        print("✅ Fenêtres importées avec succès")
        
        # Test logger
        from utils.logger import setup_logger
        logger = setup_logger("test")
        print("✅ Système de logging configuré")
        
        return True
        
    except ImportError as e:
        print(f"❌ Erreur d'import: {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_database():
    """Tester la base de données"""
    print("\n🗄️ Test de la base de données...")
    
    try:
        from database.manager import DatabaseManager
        
        db = DatabaseManager()
        db.initialize_database()
        print("✅ Base de données initialisée")
        
        # Test authentification
        user = db.authenticate_user("admin", "admin123")
        if user:
            print(f"✅ Authentification réussie: {user['username']} ({user['role']})")
        else:
            print("❌ Échec de l'authentification")
            return False
        
        db.close()
        print("✅ Connexion fermée")
        return True
        
    except Exception as e:
        print(f"❌ Erreur base de données: {e}")
        return False

def test_themes():
    """Tester le système de thèmes"""
    print("\n🎨 Test des thèmes...")
    
    try:
        from styles.themes import theme_manager, ThemeType
        
        # Test thème actuel
        current_css = theme_manager.get_current_theme_css()
        print(f"✅ CSS du thème {theme_manager.current_theme.value} chargé ({len(current_css)} caractères)")
        
        # Test basculement
        new_css = theme_manager.switch_theme()
        print(f"✅ Basculement vers {theme_manager.current_theme.value} réussi")
        
        # Retour au thème original
        theme_manager.switch_theme()
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur thèmes: {e}")
        return False

def test_fluent_widgets():
    """Tester la disponibilité des Fluent Widgets"""
    print("\n🎛️ Test des Fluent Widgets...")
    
    try:
        from qfluentwidgets import PushButton, LineEdit, FluentIcon
        print("✅ PyQt-Fluent-Widgets disponible")
        return True
    except ImportError:
        print("⚠️ PyQt-Fluent-Widgets non disponible (fallback vers PyQt5 standard)")
        return True
    except Exception as e:
        print(f"❌ Erreur Fluent Widgets: {e}")
        return False

def test_directories():
    """Tester la création des répertoires"""
    print("\n📁 Test des répertoires...")
    
    try:
        from config.settings import config
        
        directories = [
            config.DATA_DIR,
            config.REPORTS_DIR,
            config.TEMP_DIR
        ]
        
        for directory in directories:
            if directory.exists():
                print(f"✅ Répertoire existe: {directory}")
            else:
                print(f"⚠️ Répertoire manquant: {directory}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur répertoires: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🚀 Tests de l'application GSlim")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("Base de données", test_database),
        ("Thèmes CSS", test_themes),
        ("Fluent Widgets", test_fluent_widgets),
        ("Répertoires", test_directories)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur dans {test_name}: {e}")
            results.append((test_name, False))
    
    # Résumé
    print("\n" + "=" * 50)
    print("📊 Résumé des tests:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHEC"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Résultat: {passed}/{len(tests)} tests réussis")
    
    if passed == len(tests):
        print("🎉 Tous les tests sont passés ! L'application est prête.")
        print("\n💡 Pour lancer l'application: python main.py")
        print("🔑 Identifiants par défaut: admin / admin123")
    else:
        print("⚠️ Certains tests ont échoué. Vérifiez les erreurs ci-dessus.")
    
    return passed == len(tests)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
