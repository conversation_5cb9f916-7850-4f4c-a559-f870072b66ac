#!/usr/bin/env python3
"""
Test simple de l'interface GSlim
"""

import sys
from pathlib import Path

# Ajouter le répertoire src au path Python
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_login_window():
    """Tester uniquement la fenêtre de connexion"""
    print("🔐 Test de la fenêtre de connexion...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QTimer
        
        # Créer l'application
        app = QApplication(sys.argv)
        
        # Mock de l'instance app pour la fenêtre de connexion
        class MockApp:
            def get_database_manager(self):
                from database.manager import DatabaseManager
                db = DatabaseManager()
                db.initialize_database()
                return db
            
            def on_login_success(self, user):
                print(f"✅ Connexion réussie: {user}")
                app.quit()
            
            def on_closing(self):
                print("🚪 Fermeture de l'application")
                app.quit()
        
        mock_app = MockApp()
        
        # Créer et afficher la fenêtre de connexion
        from views.login_window import LoginWindow
        login_window = LoginWindow(mock_app)
        login_window.show()
        
        print("✅ Fenêtre de connexion affichée")
        print("💡 Utilisez admin/admin123 pour vous connecter")
        
        # Fermer automatiquement après 30 secondes si pas d'interaction
        QTimer.singleShot(30000, app.quit)
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return 1

def test_main_window():
    """Tester uniquement la fenêtre principale"""
    print("🏠 Test de la fenêtre principale...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QTimer
        
        # Créer l'application
        app = QApplication(sys.argv)
        
        # Mock de l'instance app pour la fenêtre principale
        class MockApp:
            def get_current_user(self):
                return {
                    'id': 1,
                    'username': 'admin',
                    'email': '<EMAIL>',
                    'role': 'admin'
                }
            
            def switch_theme(self):
                print("🎨 Changement de thème")
            
            def logout(self):
                print("🚪 Déconnexion")
                app.quit()
            
            def on_closing(self):
                print("🚪 Fermeture de l'application")
                app.quit()
        
        mock_app = MockApp()
        
        # Créer et afficher la fenêtre principale
        from views.main_window import MainWindow
        main_window = MainWindow(mock_app)
        main_window.show()
        
        print("✅ Fenêtre principale affichée")
        print("💡 Testez la navigation et les boutons")
        
        # Fermer automatiquement après 60 secondes si pas d'interaction
        QTimer.singleShot(60000, app.quit)
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """Menu de test"""
    print("🧪 Tests d'interface GSlim")
    print("=" * 40)
    print("1. Tester la fenêtre de connexion")
    print("2. Tester la fenêtre principale")
    print("3. Lancer l'application complète")
    print("=" * 40)
    
    try:
        choice = input("Votre choix (1-3): ").strip()
        
        if choice == "1":
            return test_login_window()
        elif choice == "2":
            return test_main_window()
        elif choice == "3":
            print("🚀 Lancement de l'application complète...")
            from src.app import GSlimApp
            app = GSlimApp()
            return app.run()
        else:
            print("❌ Choix invalide")
            return 1
            
    except KeyboardInterrupt:
        print("\n🛑 Arrêt demandé par l'utilisateur")
        return 0
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
