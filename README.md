# GSlim - Application de Gestion de Stock

Une application Python moderne avec interface graphique pour la gestion complète d'un système de stock.

## Fonctionnalités

- 🔐 Système de connexion sécurisé avec gestion des utilisateurs
- 📊 Tableau de bord avec statistiques et graphiques
- 📦 Gestion complète des articles (CRUD, recherche, import/export)
- 📈 Suivi des mouvements de stock (entrées/sorties)
- 🏢 Gestion des fournisseurs
- 🛒 Système de commandes avec notifications automatiques
- 📋 Génération de rapports PDF/Excel
- ⚙️ Configuration et paramètres personnalisables
- 🎨 Interface moderne avec thème sombre/clair

## Installation

1. Clonez le repository :
```bash
git clone <repository-url>
cd GSlim
```

2. Créez un environnement virtuel :
```bash
python -m venv venv
```

3. Activez l'environnement virtuel :
```bash
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

4. Installez les dépendances :
```bash
pip install -r requirements.txt
```

5. Co<PERSON>z le fichier de configuration :
```bash
cp .env.example .env
```

6. Lancez l'application :
```bash
# Méthode recommandée
python main.py

# Ou avec diagnostics
python start_app.py

# Tests complets
python test_app.py
```

## Connexion

**Identifiants par défaut :**
- Nom d'utilisateur : `admin`
- Mot de passe : `admin123`

## Structure du projet

```
GSlim/
├── main.py                 # Point d'entrée principal
├── requirements.txt        # Dépendances Python
├── .env.example           # Configuration exemple
├── README.md              # Documentation
├── src/                   # Code source
│   ├── app.py            # Application principale
│   ├── config/           # Configuration
│   ├── database/         # Gestion base de données
│   ├── models/           # Modèles de données
│   ├── views/            # Interfaces utilisateur
│   ├── controllers/      # Logique métier
│   └── utils/            # Utilitaires
├── data/                 # Base de données
├── reports/              # Rapports générés
├── temp/                 # Fichiers temporaires
└── tests/                # Tests unitaires
```

## Technologies utilisées

- **Interface** : PyQt5 + CSS + PyQt-Fluent-Widgets
- **Base de données** : SQLite
- **Sécurité** : bcrypt
- **Rapports** : ReportLab, matplotlib, plotly
- **Données** : pandas, openpyxl
- **Thèmes** : CSS personnalisé inspiré du Fluent Design

## Licence

[À définir]
