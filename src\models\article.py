"""
Modèle Article pour la gestion des articles en stock
"""

from datetime import datetime
from typing import Dict, Any, Optional, List
from decimal import Decimal

from .base import BaseModel, TimestampMixin, validate_positive_number, validate_non_empty_string


class Article(BaseModel, TimestampMixin):
    """Modèle pour les articles en stock"""
    
    def _set_defaults(self):
        """Définir les valeurs par défaut"""
        self._data.update({
            'id': None,
            'name': '',
            'reference': '',
            'description': '',
            'category_id': None,
            'category_name': '',
            'supplier_id': None,
            'supplier_name': '',
            'unit_price': Decimal('0.00'),
            'quantity_in_stock': 0,
            'minimum_stock': 0,
            'maximum_stock': None,
            'unit': 'pièce',
            'barcode': '',
            'location': '',
            'is_active': True,
            'tax_rate': Decimal('0.00'),
            'cost_price': Decimal('0.00'),
            'margin_percentage': Decimal('0.00'),
            'weight': None,
            'dimensions': '',
            'image_path': '',
            'notes': ''
        })
        self._set_timestamp_defaults()
    
    def get_validation_rules(self) -> Dict[str, Dict]:
        """Règles de validation pour les articles"""
        return {
            'name': {
                'required': True,
                'type': str,
                'min_length': 2,
                'max_length': 200,
                'validator': validate_non_empty_string,
                'validator_message': "Le nom de l'article ne peut pas être vide"
            },
            'reference': {
                'required': True,
                'type': str,
                'min_length': 1,
                'max_length': 50,
                'validator': validate_non_empty_string,
                'validator_message': "La référence ne peut pas être vide"
            },
            'unit_price': {
                'required': True,
                'type': (Decimal, float, int),
                'min_value': 0,
                'validator': validate_positive_number,
                'validator_message': "Le prix unitaire doit être positif"
            },
            'quantity_in_stock': {
                'required': True,
                'type': int,
                'min_value': 0,
                'validator_message': "La quantité en stock doit être positive"
            },
            'minimum_stock': {
                'required': True,
                'type': int,
                'min_value': 0,
                'validator_message': "Le stock minimum doit être positif"
            },
            'maximum_stock': {
                'type': (int, type(None)),
                'min_value': 0,
                'validator_message': "Le stock maximum doit être positif"
            },
            'cost_price': {
                'type': (Decimal, float, int),
                'min_value': 0,
                'validator': validate_positive_number,
                'validator_message': "Le prix de revient doit être positif"
            },
            'tax_rate': {
                'type': (Decimal, float, int),
                'min_value': 0,
                'max_value': 100,
                'validator_message': "Le taux de TVA doit être entre 0 et 100%"
            },
            'margin_percentage': {
                'type': (Decimal, float, int),
                'min_value': 0,
                'validator_message': "Le pourcentage de marge doit être positif"
            }
        }
    
    # Propriétés calculées
    def get_selling_price_with_tax(self) -> Decimal:
        """Calculer le prix de vente TTC"""
        unit_price = Decimal(str(self._data['unit_price']))
        tax_rate = Decimal(str(self._data['tax_rate']))
        return unit_price * (1 + tax_rate / 100)
    
    def get_total_value(self) -> Decimal:
        """Calculer la valeur totale du stock"""
        unit_price = Decimal(str(self._data['unit_price']))
        quantity = self._data['quantity_in_stock']
        return unit_price * quantity
    
    def get_margin_amount(self) -> Decimal:
        """Calculer le montant de la marge"""
        unit_price = Decimal(str(self._data['unit_price']))
        cost_price = Decimal(str(self._data['cost_price']))
        return unit_price - cost_price
    
    def calculate_margin_percentage(self) -> Decimal:
        """Calculer le pourcentage de marge"""
        cost_price = Decimal(str(self._data['cost_price']))
        if cost_price == 0:
            return Decimal('0')
        margin = self.get_margin_amount()
        return (margin / cost_price) * 100
    
    def is_low_stock(self) -> bool:
        """Vérifier si le stock est bas"""
        return self._data['quantity_in_stock'] <= self._data['minimum_stock']
    
    def is_out_of_stock(self) -> bool:
        """Vérifier si l'article est en rupture de stock"""
        return self._data['quantity_in_stock'] == 0
    
    def is_overstocked(self) -> bool:
        """Vérifier si l'article est en surstock"""
        max_stock = self._data['maximum_stock']
        if max_stock is None:
            return False
        return self._data['quantity_in_stock'] > max_stock
    
    def get_stock_status(self) -> str:
        """Obtenir le statut du stock"""
        if self.is_out_of_stock():
            return "Rupture de stock"
        elif self.is_low_stock():
            return "Stock bas"
        elif self.is_overstocked():
            return "Surstock"
        else:
            return "Stock normal"
    
    def get_stock_status_color(self) -> str:
        """Obtenir la couleur associée au statut du stock"""
        if self.is_out_of_stock():
            return "#dc3545"  # Rouge
        elif self.is_low_stock():
            return "#fd7e14"  # Orange
        elif self.is_overstocked():
            return "#6f42c1"  # Violet
        else:
            return "#28a745"  # Vert
    
    def can_sell(self, quantity: int) -> bool:
        """Vérifier si on peut vendre une quantité donnée"""
        return self._data['quantity_in_stock'] >= quantity and self._data['is_active']
    
    def get_reorder_quantity(self) -> int:
        """Calculer la quantité à recommander"""
        max_stock = self._data['maximum_stock']
        current_stock = self._data['quantity_in_stock']
        
        if max_stock is None:
            # Si pas de stock max défini, recommander 2x le minimum
            return max(0, (self._data['minimum_stock'] * 2) - current_stock)
        else:
            return max(0, max_stock - current_stock)
    
    # Méthodes de mise à jour du stock
    def add_stock(self, quantity: int, reason: str = "Ajout manuel") -> bool:
        """Ajouter du stock"""
        if quantity <= 0:
            return False
        
        self._data['quantity_in_stock'] += quantity
        self.touch()
        self.logger.info(f"Stock ajouté: +{quantity} pour {self._data['name']} ({reason})")
        return True
    
    def remove_stock(self, quantity: int, reason: str = "Retrait manuel") -> bool:
        """Retirer du stock"""
        if quantity <= 0 or quantity > self._data['quantity_in_stock']:
            return False
        
        self._data['quantity_in_stock'] -= quantity
        self.touch()
        self.logger.info(f"Stock retiré: -{quantity} pour {self._data['name']} ({reason})")
        return True
    
    def set_stock(self, quantity: int, reason: str = "Ajustement") -> bool:
        """Définir la quantité en stock"""
        if quantity < 0:
            return False
        
        old_quantity = self._data['quantity_in_stock']
        self._data['quantity_in_stock'] = quantity
        self.touch()
        self.logger.info(f"Stock ajusté: {old_quantity} -> {quantity} pour {self._data['name']} ({reason})")
        return True
    
    # Méthodes de formatage
    def format_price(self, price_field: str = 'unit_price') -> str:
        """Formater un prix pour l'affichage"""
        price = self._data.get(price_field, 0)
        return f"{price:.2f} €"
    
    def format_stock_info(self) -> str:
        """Formater les informations de stock"""
        current = self._data['quantity_in_stock']
        minimum = self._data['minimum_stock']
        maximum = self._data['maximum_stock']
        
        if maximum:
            return f"{current} / {minimum}-{maximum} {self._data['unit']}"
        else:
            return f"{current} (min: {minimum}) {self._data['unit']}"
    
    # Méthodes de recherche et filtrage
    def matches_search(self, search_term: str) -> bool:
        """Vérifier si l'article correspond à un terme de recherche"""
        search_term = search_term.lower()
        searchable_fields = [
            self._data['name'],
            self._data['reference'],
            self._data['description'],
            self._data['category_name'],
            self._data['supplier_name'],
            self._data['barcode']
        ]
        
        return any(search_term in str(field).lower() for field in searchable_fields if field)
    
    def to_display_dict(self) -> Dict[str, Any]:
        """Convertir en dictionnaire pour l'affichage"""
        data = self.to_dict()
        data.update({
            'formatted_unit_price': self.format_price('unit_price'),
            'formatted_cost_price': self.format_price('cost_price'),
            'formatted_selling_price_with_tax': f"{self.get_selling_price_with_tax():.2f} €",
            'formatted_total_value': f"{self.get_total_value():.2f} €",
            'formatted_margin': f"{self.get_margin_amount():.2f} €",
            'calculated_margin_percentage': f"{self.calculate_margin_percentage():.1f}%",
            'stock_status': self.get_stock_status(),
            'stock_status_color': self.get_stock_status_color(),
            'formatted_stock_info': self.format_stock_info(),
            'reorder_quantity': self.get_reorder_quantity(),
            'is_low_stock': self.is_low_stock(),
            'is_out_of_stock': self.is_out_of_stock(),
            'is_overstocked': self.is_overstocked()
        })
        return data


# Fonctions utilitaires pour les articles
def create_article_from_dict(data: Dict[str, Any]) -> Article:
    """Créer un article à partir d'un dictionnaire"""
    article = Article(**data)
    if not article.validate():
        from .base import ValidationError
        raise ValidationError(article.get_errors())
    return article


def calculate_selling_price_from_cost(cost_price: float, margin_percentage: float) -> float:
    """Calculer le prix de vente à partir du prix de revient et de la marge"""
    return cost_price * (1 + margin_percentage / 100)


def calculate_cost_from_selling_price(selling_price: float, margin_percentage: float) -> float:
    """Calculer le prix de revient à partir du prix de vente et de la marge"""
    if margin_percentage >= 100:
        raise ValueError("La marge ne peut pas être supérieure ou égale à 100%")
    return selling_price / (1 + margin_percentage / 100)
