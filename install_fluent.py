#!/usr/bin/env python3
"""
Script d'installation pour PyQt-Fluent-Widgets
"""

import subprocess
import sys

def install_fluent_widgets():
    """Installer PyQt-Fluent-Widgets"""
    print("🎨 Installation de PyQt-Fluent-Widgets...")
    
    try:
        # Essayer d'installer PyQt-Fluent-Widgets
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            "PyQt-Fluent-Widgets==1.5.6"
        ])
        print("✅ PyQt-Fluent-Widgets installé avec succès!")
        
        # Test d'import
        try:
            from qfluentwidgets import PushButton
            print("✅ Import test réussi!")
            return True
        except ImportError:
            print("⚠️ Installation réussie mais import échoué")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Erreur d'installation: {e}")
        print("💡 L'application fonctionnera avec les widgets PyQt5 standard")
        return False
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        return False

def main():
    """Fonction principale"""
    print("🚀 Installation des composants Fluent pour GSlim")
    print("=" * 50)
    
    success = install_fluent_widgets()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Installation terminée avec succès!")
        print("💡 L'application utilisera les Fluent Widgets pour une interface moderne")
    else:
        print("⚠️ Installation échouée ou partielle")
        print("💡 L'application fonctionnera avec l'interface PyQt5 standard")
    
    print("\n🚀 Vous pouvez maintenant lancer l'application avec:")
    print("   python main.py")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
