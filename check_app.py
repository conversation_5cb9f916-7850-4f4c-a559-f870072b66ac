#!/usr/bin/env python3
"""
Vérification rapide de l'application GSlim
"""

import sys
import os
from pathlib import Path

# Ajouter le répertoire src au path Python
sys.path.insert(0, str(Path(__file__).parent / "src"))

def quick_check():
    """Vérification rapide sans interface graphique"""
    print("⚡ Vérification rapide de GSlim")
    print("=" * 40)
    
    errors = []
    
    # 1. Test des imports
    print("📦 Vérification des imports...")
    try:
        from config.settings import config
        print(f"  ✅ Configuration: {config.APP_NAME} v{config.APP_VERSION}")
    except Exception as e:
        errors.append(f"Configuration: {e}")
        print(f"  ❌ Configuration: {e}")
    
    try:
        from database.manager import DatabaseManager
        print("  ✅ Database Manager")
    except Exception as e:
        errors.append(f"Database Manager: {e}")
        print(f"  ❌ Database Manager: {e}")
    
    try:
        from PyQt5.QtWidgets import QApplication
        print("  ✅ PyQt5")
    except Exception as e:
        errors.append(f"PyQt5: {e}")
        print(f"  ❌ PyQt5: {e}")
    
    try:
        from styles.themes import theme_manager
        print("  ✅ Gestionnaire de thèmes")
    except Exception as e:
        errors.append(f"Thèmes: {e}")
        print(f"  ❌ Thèmes: {e}")
    
    # 2. Test Fluent Widgets
    print("\n🎨 Vérification des Fluent Widgets...")
    try:
        from qfluentwidgets import PushButton
        print("  ✅ PyQt-Fluent-Widgets disponible")
    except ImportError:
        print("  ⚠️ PyQt-Fluent-Widgets non disponible (mode standard)")
    except Exception as e:
        print(f"  ❌ Erreur Fluent Widgets: {e}")
    
    # 3. Test base de données
    print("\n🗄️ Vérification de la base de données...")
    try:
        from database.manager import DatabaseManager
        db = DatabaseManager()
        db.initialize_database()
        
        # Test authentification
        user = db.authenticate_user("admin", "admin123")
        if user:
            print(f"  ✅ Utilisateur admin: {user['username']} ({user['role']})")
        else:
            print("  ❌ Échec authentification admin")
            errors.append("Authentification admin échouée")
        
        db.close()
        
    except Exception as e:
        errors.append(f"Base de données: {e}")
        print(f"  ❌ Base de données: {e}")
    
    # 4. Test des vues
    print("\n🖼️ Vérification des vues...")
    try:
        from views.login_window import LoginWindow
        from views.main_window import MainWindow
        print("  ✅ Fenêtres importées")
    except Exception as e:
        errors.append(f"Vues: {e}")
        print(f"  ❌ Vues: {e}")
    
    # 5. Test de l'application principale
    print("\n🚀 Vérification de l'application...")
    try:
        from src.app import GSlimApp
        print("  ✅ GSlimApp importée")
    except Exception as e:
        errors.append(f"Application: {e}")
        print(f"  ❌ Application: {e}")
    
    # Résumé
    print("\n" + "=" * 40)
    if not errors:
        print("🎉 Toutes les vérifications sont passées !")
        print("\n💡 L'application est prête à être lancée:")
        print("   python main.py")
        print("\n🔑 Identifiants par défaut:")
        print("   Utilisateur: admin")
        print("   Mot de passe: admin123")
        return True
    else:
        print(f"❌ {len(errors)} erreur(s) détectée(s):")
        for i, error in enumerate(errors, 1):
            print(f"   {i}. {error}")
        print("\n💡 Corrigez ces erreurs avant de lancer l'application")
        return False

def main():
    """Fonction principale"""
    try:
        success = quick_check()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n🛑 Vérification interrompue")
        return 1
    except Exception as e:
        print(f"\n❌ Erreur inattendue: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
