"""
Fenêtre de gestion des articles
Interface complète pour CRUD articles avec recherche et filtrage
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QTableWidget, QTableWidgetItem, QLineEdit, QComboBox,
    QFrame, QSplitter, QHeaderView, QAbstractItemView,
    QMessageBox, QDialog, QFormLayout, QSpinBox, QDoubleSpinBox,
    QTextEdit, QCheckBox, QGroupBox, QGridLayout
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QColor, QPalette

try:
    from qfluentwidgets import (
        PushButton, LineEdit, ComboBox, TableWidget,
        SearchLineEdit, CardWidget, TitleLabel, CaptionLabel,
        FluentIcon, InfoBar, InfoBarPosition
    )
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False

from controllers.article_controller import ArticleController
from utils.logger import setup_logger


class ArticlesWindow(QWidget):
    """Fenêtre de gestion des articles"""
    
    article_selected = pyqtSignal(dict)
    
    def __init__(self, app_instance):
        super().__init__()
        self.app_instance = app_instance
        self.logger = setup_logger(__name__)
        self.controller = ArticleController(app_instance.get_database_manager())
        
        # Variables d'état
        self.current_articles = []
        self.selected_article = None
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self._perform_search)
        
        self._init_ui()
        self._connect_signals()
        self._load_articles()
    
    def _init_ui(self):
        """Initialiser l'interface utilisateur"""
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # En-tête
        self._create_header(layout)
        
        # Barre d'outils et recherche
        self._create_toolbar(layout)
        
        # Zone principale avec splitter
        self._create_main_area(layout)
        
        # Barre de statut
        self._create_status_bar(layout)
        
        self.setLayout(layout)
    
    def _create_header(self, layout):
        """Créer l'en-tête"""
        header_layout = QHBoxLayout()
        
        if FLUENT_AVAILABLE:
            title = TitleLabel("Gestion des Articles")
        else:
            title = QLabel("Gestion des Articles")
            title.setProperty("class", "title")
            font = QFont("Segoe UI", 24, QFont.Bold)
            title.setFont(font)
        
        header_layout.addWidget(title)
        header_layout.addStretch()
        
        # Statistiques rapides
        self._create_stats_cards(header_layout)
        
        layout.addLayout(header_layout)
    
    def _create_stats_cards(self, layout):
        """Créer les cartes de statistiques"""
        stats = self.controller.get_stock_statistics()
        
        # Carte total articles
        total_card = self._create_stat_card("Articles", str(stats['total_articles']), "#0078d4")
        layout.addWidget(total_card)
        
        # Carte stock bas
        low_card = self._create_stat_card("Stock bas", str(stats['low_stock']), "#fd7e14")
        layout.addWidget(low_card)
        
        # Carte rupture
        out_card = self._create_stat_card("Ruptures", str(stats['out_of_stock']), "#dc3545")
        layout.addWidget(out_card)
        
        # Carte valeur totale
        value_card = self._create_stat_card("Valeur", f"{stats['total_value']:.0f}€", "#28a745")
        layout.addWidget(value_card)
    
    def _create_stat_card(self, title: str, value: str, color: str):
        """Créer une carte de statistique"""
        if FLUENT_AVAILABLE:
            card = CardWidget()
            card.setFixedSize(120, 80)
        else:
            card = QFrame()
            card.setProperty("class", "card")
            card.setFixedSize(120, 80)
        
        card_layout = QVBoxLayout()
        card_layout.setContentsMargins(10, 10, 10, 10)
        card_layout.setSpacing(5)
        
        # Valeur
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet(f"color: {color}; font-size: 18px; font-weight: bold;")
        card_layout.addWidget(value_label)
        
        # Titre
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #666; font-size: 12px;")
        card_layout.addWidget(title_label)
        
        card.setLayout(card_layout)
        return card
    
    def _create_toolbar(self, layout):
        """Créer la barre d'outils"""
        toolbar_layout = QHBoxLayout()
        
        # Recherche
        if FLUENT_AVAILABLE:
            self.search_input = SearchLineEdit()
            self.search_input.setPlaceholderText("Rechercher un article...")
        else:
            self.search_input = QLineEdit()
            self.search_input.setPlaceholderText("Rechercher un article...")
        
        self.search_input.setFixedWidth(300)
        toolbar_layout.addWidget(self.search_input)
        
        # Filtres
        self.category_filter = QComboBox()
        self.category_filter.addItem("Toutes les catégories", None)
        self.category_filter.setFixedWidth(200)
        toolbar_layout.addWidget(self.category_filter)
        
        self.stock_filter = QComboBox()
        self.stock_filter.addItems(["Tous les stocks", "Stock normal", "Stock bas", "Rupture"])
        self.stock_filter.setFixedWidth(150)
        toolbar_layout.addWidget(self.stock_filter)
        
        toolbar_layout.addStretch()
        
        # Boutons d'action
        if FLUENT_AVAILABLE:
            self.add_button = PushButton("Nouveau")
            self.add_button.setIcon(FluentIcon.ADD)
            self.edit_button = PushButton("Modifier")
            self.edit_button.setIcon(FluentIcon.EDIT)
            self.delete_button = PushButton("Supprimer")
            self.delete_button.setIcon(FluentIcon.DELETE)
            self.refresh_button = PushButton("Actualiser")
            self.refresh_button.setIcon(FluentIcon.SYNC)
        else:
            self.add_button = QPushButton("Nouveau")
            self.edit_button = QPushButton("Modifier")
            self.delete_button = QPushButton("Supprimer")
            self.refresh_button = QPushButton("Actualiser")
        
        # Désactiver les boutons par défaut
        self.edit_button.setEnabled(False)
        self.delete_button.setEnabled(False)
        
        toolbar_layout.addWidget(self.add_button)
        toolbar_layout.addWidget(self.edit_button)
        toolbar_layout.addWidget(self.delete_button)
        toolbar_layout.addWidget(self.refresh_button)
        
        layout.addLayout(toolbar_layout)
    
    def _create_main_area(self, layout):
        """Créer la zone principale"""
        splitter = QSplitter(Qt.Horizontal)
        
        # Table des articles
        self._create_articles_table(splitter)
        
        # Panneau de détails
        self._create_details_panel(splitter)
        
        # Configuration du splitter
        splitter.setSizes([700, 300])
        splitter.setCollapsible(0, False)
        splitter.setCollapsible(1, True)
        
        layout.addWidget(splitter)
    
    def _create_articles_table(self, parent):
        """Créer la table des articles"""
        if FLUENT_AVAILABLE:
            self.articles_table = TableWidget()
        else:
            self.articles_table = QTableWidget()
        
        # Configuration de la table
        columns = [
            "Référence", "Nom", "Catégorie", "Stock", "Prix unitaire", 
            "Valeur totale", "Statut", "Fournisseur"
        ]
        
        self.articles_table.setColumnCount(len(columns))
        self.articles_table.setHorizontalHeaderLabels(columns)
        
        # Configuration des colonnes
        header = self.articles_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Référence
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # Nom
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Catégorie
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Stock
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Prix
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Valeur
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Statut
        
        # Configuration du comportement
        self.articles_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.articles_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.articles_table.setAlternatingRowColors(True)
        self.articles_table.setSortingEnabled(True)
        
        parent.addWidget(self.articles_table)
    
    def _create_details_panel(self, parent):
        """Créer le panneau de détails"""
        if FLUENT_AVAILABLE:
            details_card = CardWidget()
        else:
            details_card = QFrame()
            details_card.setProperty("class", "card")
        
        details_layout = QVBoxLayout()
        details_layout.setContentsMargins(15, 15, 15, 15)
        details_layout.setSpacing(10)
        
        # Titre
        if FLUENT_AVAILABLE:
            details_title = TitleLabel("Détails de l'article")
        else:
            details_title = QLabel("Détails de l'article")
            details_title.setProperty("class", "subtitle")
        
        details_layout.addWidget(details_title)
        
        # Zone de contenu des détails
        self.details_content = QLabel("Sélectionnez un article pour voir ses détails")
        self.details_content.setWordWrap(True)
        self.details_content.setAlignment(Qt.AlignTop)
        details_layout.addWidget(self.details_content)
        
        details_layout.addStretch()
        details_card.setLayout(details_layout)
        
        parent.addWidget(details_card)
    
    def _create_status_bar(self, layout):
        """Créer la barre de statut"""
        self.status_label = QLabel("Prêt")
        self.status_label.setProperty("class", "caption")
        layout.addWidget(self.status_label)
    
    def _connect_signals(self):
        """Connecter les signaux"""
        # Recherche avec délai
        self.search_input.textChanged.connect(self._on_search_changed)
        
        # Filtres
        self.category_filter.currentTextChanged.connect(self._apply_filters)
        self.stock_filter.currentTextChanged.connect(self._apply_filters)
        
        # Boutons
        self.add_button.clicked.connect(self._add_article)
        self.edit_button.clicked.connect(self._edit_article)
        self.delete_button.clicked.connect(self._delete_article)
        self.refresh_button.clicked.connect(self._load_articles)
        
        # Table
        self.articles_table.itemSelectionChanged.connect(self._on_selection_changed)
        self.articles_table.itemDoubleClicked.connect(self._edit_article)
    
    def _load_articles(self):
        """Charger les articles"""
        try:
            self.status_label.setText("Chargement des articles...")
            self.current_articles = self.controller.get_articles_with_details()
            self._populate_table()
            self._update_stats()
            self.status_label.setText(f"{len(self.current_articles)} article(s) chargé(s)")
            
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des articles: {e}")
            self._show_error("Erreur", f"Impossible de charger les articles: {e}")
    
    def _populate_table(self):
        """Remplir la table avec les articles"""
        self.articles_table.setRowCount(len(self.current_articles))
        
        for row, article in enumerate(self.current_articles):
            # Référence
            self.articles_table.setItem(row, 0, QTableWidgetItem(article.get('reference', '')))
            
            # Nom
            self.articles_table.setItem(row, 1, QTableWidgetItem(article.get('name', '')))
            
            # Catégorie
            self.articles_table.setItem(row, 2, QTableWidgetItem(article.get('category_name', 'Aucune')))
            
            # Stock
            stock_item = QTableWidgetItem(article.get('formatted_stock_info', ''))
            if article.get('is_out_of_stock'):
                stock_item.setBackground(QColor("#ffebee"))
            elif article.get('is_low_stock'):
                stock_item.setBackground(QColor("#fff3e0"))
            self.articles_table.setItem(row, 3, stock_item)
            
            # Prix unitaire
            self.articles_table.setItem(row, 4, QTableWidgetItem(article.get('formatted_unit_price', '')))
            
            # Valeur totale
            self.articles_table.setItem(row, 5, QTableWidgetItem(article.get('formatted_total_value', '')))
            
            # Statut
            status_item = QTableWidgetItem(article.get('stock_status', ''))
            status_color = article.get('stock_status_color', '#000000')
            status_item.setForeground(QColor(status_color))
            self.articles_table.setItem(row, 6, status_item)
            
            # Fournisseur
            self.articles_table.setItem(row, 7, QTableWidgetItem(article.get('supplier_name', 'Aucun')))
    
    def _update_stats(self):
        """Mettre à jour les statistiques"""
        # Cette méthode sera appelée pour rafraîchir les cartes de stats
        pass
    
    def _on_search_changed(self):
        """Gérer le changement de recherche avec délai"""
        self.search_timer.stop()
        self.search_timer.start(500)  # Délai de 500ms
    
    def _perform_search(self):
        """Effectuer la recherche"""
        search_term = self.search_input.text().strip()
        
        if search_term:
            self.status_label.setText(f"Recherche: '{search_term}'...")
            filtered_articles = []
            
            for article in self.current_articles:
                if (search_term.lower() in article.get('name', '').lower() or
                    search_term.lower() in article.get('reference', '').lower() or
                    search_term.lower() in article.get('description', '').lower()):
                    filtered_articles.append(article)
            
            self.current_articles = filtered_articles
            self.status_label.setText(f"{len(filtered_articles)} article(s) trouvé(s)")
        else:
            self._load_articles()
        
        self._populate_table()
    
    def _apply_filters(self):
        """Appliquer les filtres"""
        # Recharger tous les articles puis appliquer les filtres
        all_articles = self.controller.get_articles_with_details()
        filtered_articles = []
        
        category_filter = self.category_filter.currentData()
        stock_filter = self.stock_filter.currentText()
        
        for article in all_articles:
            # Filtre catégorie
            if category_filter and article.get('category_id') != category_filter:
                continue
            
            # Filtre stock
            if stock_filter == "Stock bas" and not article.get('is_low_stock'):
                continue
            elif stock_filter == "Rupture" and not article.get('is_out_of_stock'):
                continue
            elif stock_filter == "Stock normal" and (article.get('is_low_stock') or article.get('is_out_of_stock')):
                continue
            
            filtered_articles.append(article)
        
        self.current_articles = filtered_articles
        self._populate_table()
        self.status_label.setText(f"{len(filtered_articles)} article(s) après filtrage")
    
    def _on_selection_changed(self):
        """Gérer le changement de sélection"""
        selected_rows = self.articles_table.selectionModel().selectedRows()
        
        if selected_rows:
            row = selected_rows[0].row()
            if 0 <= row < len(self.current_articles):
                self.selected_article = self.current_articles[row]
                self._update_details_panel()
                self.edit_button.setEnabled(True)
                self.delete_button.setEnabled(True)
                self.article_selected.emit(self.selected_article)
        else:
            self.selected_article = None
            self._clear_details_panel()
            self.edit_button.setEnabled(False)
            self.delete_button.setEnabled(False)
    
    def _update_details_panel(self):
        """Mettre à jour le panneau de détails"""
        if not self.selected_article:
            return
        
        article = self.selected_article
        details_html = f"""
        <h3>{article.get('name', 'N/A')}</h3>
        <p><b>Référence:</b> {article.get('reference', 'N/A')}</p>
        <p><b>Description:</b> {article.get('description', 'Aucune')}</p>
        <p><b>Catégorie:</b> {article.get('category_name', 'Aucune')}</p>
        <p><b>Fournisseur:</b> {article.get('supplier_name', 'Aucun')}</p>
        <hr>
        <p><b>Stock actuel:</b> {article.get('quantity_in_stock', 0)} {article.get('unit', 'pièce')}</p>
        <p><b>Stock minimum:</b> {article.get('minimum_stock', 0)}</p>
        <p><b>Statut:</b> <span style="color: {article.get('stock_status_color', '#000')}">{article.get('stock_status', 'N/A')}</span></p>
        <hr>
        <p><b>Prix unitaire:</b> {article.get('formatted_unit_price', 'N/A')}</p>
        <p><b>Valeur totale:</b> {article.get('formatted_total_value', 'N/A')}</p>
        <p><b>Marge:</b> {article.get('calculated_margin_percentage', 'N/A')}</p>
        """
        
        self.details_content.setText(details_html)
    
    def _clear_details_panel(self):
        """Vider le panneau de détails"""
        self.details_content.setText("Sélectionnez un article pour voir ses détails")
    
    def _add_article(self):
        """Ajouter un nouvel article"""
        dialog = ArticleDialog(self, "Nouvel Article")
        if dialog.exec_() == QDialog.Accepted:
            article_data = dialog.get_article_data()
            result = self.controller.create_article(article_data)
            
            if result.success:
                self._show_success("Succès", result.message)
                self._load_articles()
            else:
                self._show_error("Erreur", result.message)
    
    def _edit_article(self):
        """Modifier l'article sélectionné"""
        if not self.selected_article:
            return
        
        dialog = ArticleDialog(self, "Modifier Article", self.selected_article)
        if dialog.exec_() == QDialog.Accepted:
            article_data = dialog.get_article_data()
            result = self.controller.update_article(self.selected_article['id'], article_data)
            
            if result.success:
                self._show_success("Succès", result.message)
                self._load_articles()
            else:
                self._show_error("Erreur", result.message)
    
    def _delete_article(self):
        """Supprimer l'article sélectionné"""
        if not self.selected_article:
            return
        
        reply = QMessageBox.question(
            self,
            "Confirmer la suppression",
            f"Êtes-vous sûr de vouloir supprimer l'article '{self.selected_article['name']}'?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            success, message = self.controller.delete(self.selected_article['id'])
            
            if success:
                self._show_success("Succès", message)
                self._load_articles()
            else:
                self._show_error("Erreur", message)
    
    def _show_success(self, title: str, message: str):
        """Afficher un message de succès"""
        if FLUENT_AVAILABLE:
            InfoBar.success(title, message, parent=self)
        else:
            QMessageBox.information(self, title, message)
    
    def _show_error(self, title: str, message: str):
        """Afficher un message d'erreur"""
        if FLUENT_AVAILABLE:
            InfoBar.error(title, message, parent=self)
        else:
            QMessageBox.critical(self, title, message)


class ArticleDialog(QDialog):
    """Dialog pour créer/modifier un article"""
    
    def __init__(self, parent, title: str, article_data: dict = None):
        super().__init__(parent)
        self.article_data = article_data or {}
        
        self.setWindowTitle(title)
        self.setModal(True)
        self.setFixedSize(500, 600)
        
        self._init_ui()
        self._populate_fields()
    
    def _init_ui(self):
        """Initialiser l'interface du dialog"""
        layout = QVBoxLayout()
        
        # Formulaire
        form_layout = QFormLayout()
        
        # Champs de base
        self.name_input = QLineEdit()
        self.reference_input = QLineEdit()
        self.description_input = QTextEdit()
        self.description_input.setMaximumHeight(80)
        
        form_layout.addRow("Nom*:", self.name_input)
        form_layout.addRow("Référence*:", self.reference_input)
        form_layout.addRow("Description:", self.description_input)
        
        # Prix et stock
        self.unit_price_input = QDoubleSpinBox()
        self.unit_price_input.setRange(0, 999999.99)
        self.unit_price_input.setDecimals(2)
        self.unit_price_input.setSuffix(" €")
        
        self.quantity_input = QSpinBox()
        self.quantity_input.setRange(0, 999999)
        
        self.minimum_stock_input = QSpinBox()
        self.minimum_stock_input.setRange(0, 999999)
        
        form_layout.addRow("Prix unitaire*:", self.unit_price_input)
        form_layout.addRow("Quantité en stock*:", self.quantity_input)
        form_layout.addRow("Stock minimum*:", self.minimum_stock_input)
        
        # Unité
        self.unit_input = QLineEdit()
        self.unit_input.setText("pièce")
        form_layout.addRow("Unité:", self.unit_input)
        
        # Statut actif
        self.is_active_checkbox = QCheckBox("Article actif")
        self.is_active_checkbox.setChecked(True)
        form_layout.addRow("", self.is_active_checkbox)
        
        layout.addLayout(form_layout)
        
        # Boutons
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()
        
        self.save_button = QPushButton("Enregistrer")
        self.cancel_button = QPushButton("Annuler")
        
        self.save_button.clicked.connect(self.accept)
        self.cancel_button.clicked.connect(self.reject)
        
        buttons_layout.addWidget(self.save_button)
        buttons_layout.addWidget(self.cancel_button)
        
        layout.addLayout(buttons_layout)
        self.setLayout(layout)
    
    def _populate_fields(self):
        """Remplir les champs avec les données existantes"""
        if self.article_data:
            self.name_input.setText(self.article_data.get('name', ''))
            self.reference_input.setText(self.article_data.get('reference', ''))
            self.description_input.setPlainText(self.article_data.get('description', ''))
            self.unit_price_input.setValue(float(self.article_data.get('unit_price', 0)))
            self.quantity_input.setValue(self.article_data.get('quantity_in_stock', 0))
            self.minimum_stock_input.setValue(self.article_data.get('minimum_stock', 0))
            self.unit_input.setText(self.article_data.get('unit', 'pièce'))
            self.is_active_checkbox.setChecked(self.article_data.get('is_active', True))
    
    def get_article_data(self) -> dict:
        """Récupérer les données du formulaire"""
        return {
            'name': self.name_input.text().strip(),
            'reference': self.reference_input.text().strip(),
            'description': self.description_input.toPlainText().strip(),
            'unit_price': self.unit_price_input.value(),
            'quantity_in_stock': self.quantity_input.value(),
            'minimum_stock': self.minimum_stock_input.value(),
            'unit': self.unit_input.text().strip() or 'pièce',
            'is_active': self.is_active_checkbox.isChecked()
        }
