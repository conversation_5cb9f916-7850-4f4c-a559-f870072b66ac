#!/usr/bin/env python3
"""
Script de débogage pour GSlim avec capture d'erreurs détaillée
"""

import sys
import os
import traceback
from pathlib import Path

# Ajouter le répertoire src au path Python
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_configuration():
    """Tester la configuration"""
    print("🔧 Test de la configuration...")
    try:
        from config.settings import config
        print(f"✅ APP_NAME: {config.APP_NAME}")
        print(f"✅ APP_VERSION: {config.APP_VERSION}")
        print(f"✅ ROOT_DIR: {config.ROOT_DIR}")
        print(f"✅ DATABASE_PATH: {config.DATABASE_PATH}")
        print(f"✅ DATA_DIR: {config.DATA_DIR}")
        return True
    except Exception as e:
        print(f"❌ Erreur configuration: {e}")
        traceback.print_exc()
        return False

def test_database():
    """Tester la base de données"""
    print("\n🗄️ Test de la base de données...")
    try:
        from database.manager import DatabaseManager
        db = DatabaseManager()
        print(f"✅ DatabaseManager créé")
        print(f"✅ Chemin DB: {db.db_path}")
        
        # Test d'initialisation
        db.initialize_database()
        print("✅ Base de données initialisée")
        
        # Test d'authentification
        user = db.authenticate_user("admin", "admin123")
        if user:
            print(f"✅ Utilisateur admin trouvé: {user}")
        else:
            print("⚠️ Utilisateur admin non trouvé")
        
        db.close()
        return True
    except Exception as e:
        print(f"❌ Erreur base de données: {e}")
        traceback.print_exc()
        return False

def test_pyqt5():
    """Tester PyQt5"""
    print("\n🖥️ Test de PyQt5...")
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        print("✅ PyQt5 importé")
        
        # Test création application
        app = QApplication([])
        print("✅ QApplication créée")
        
        app.quit()
        print("✅ QApplication fermée")
        return True
    except Exception as e:
        print(f"❌ Erreur PyQt5: {e}")
        traceback.print_exc()
        return False

def test_fluent_widgets():
    """Tester Fluent Widgets"""
    print("\n🎨 Test des Fluent Widgets...")
    try:
        from qfluentwidgets import PushButton, LineEdit, FluentIcon
        print("✅ PyQt-Fluent-Widgets disponible")
        return True
    except ImportError:
        print("⚠️ PyQt-Fluent-Widgets non disponible (utilisation PyQt5 standard)")
        return True
    except Exception as e:
        print(f"❌ Erreur Fluent Widgets: {e}")
        traceback.print_exc()
        return False

def test_views():
    """Tester les vues"""
    print("\n🖼️ Test des vues...")
    try:
        from views.login_window import LoginWindow
        print("✅ LoginWindow importée")
        
        from views.main_window import MainWindow
        print("✅ MainWindow importée")
        return True
    except Exception as e:
        print(f"❌ Erreur vues: {e}")
        traceback.print_exc()
        return False

def test_app_creation():
    """Tester la création de l'application"""
    print("\n🚀 Test de création de l'application...")
    try:
        from src.app import GSlimApp
        print("✅ GSlimApp importée")
        
        # Créer l'instance (sans lancer l'interface)
        app = GSlimApp()
        print("✅ GSlimApp créée")
        
        print(f"✅ Database manager: {app.db_manager is not None}")
        print(f"✅ Logger: {app.logger is not None}")
        
        return True
    except Exception as e:
        print(f"❌ Erreur création app: {e}")
        traceback.print_exc()
        return False

def run_full_app():
    """Lancer l'application complète"""
    print("\n🎯 Lancement de l'application complète...")
    try:
        from src.app import GSlimApp
        
        print("📱 Création de l'application...")
        app = GSlimApp()
        
        print("🚀 Lancement de l'interface...")
        return app.run()
        
    except Exception as e:
        print(f"❌ Erreur lancement: {e}")
        traceback.print_exc()
        return 1

def main():
    """Fonction principale de débogage"""
    print("🔍 Débogage complet de GSlim")
    print("=" * 60)
    
    tests = [
        ("Configuration", test_configuration),
        ("Base de données", test_database),
        ("PyQt5", test_pyqt5),
        ("Fluent Widgets", test_fluent_widgets),
        ("Vues", test_views),
        ("Création App", test_app_creation)
    ]
    
    all_passed = True
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if not result:
                all_passed = False
        except Exception as e:
            print(f"❌ Erreur dans {test_name}: {e}")
            all_passed = False
    
    print("\n" + "=" * 60)
    
    if all_passed:
        print("🎉 Tous les tests sont passés !")
        print("\n🚀 Lancement de l'application...")
        return run_full_app()
    else:
        print("❌ Certains tests ont échoué.")
        print("💡 Vérifiez les erreurs ci-dessus avant de lancer l'application.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
