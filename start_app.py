#!/usr/bin/env python3
"""
Script de démarrage simple pour GSlim
"""

import sys
import os
from pathlib import Path

# Ajouter le répertoire src au path Python
sys.path.insert(0, str(Path(__file__).parent / "src"))

def main():
    """Démarrer l'application avec gestion d'erreurs"""
    print("🚀 Démarrage de GSlim...")
    
    try:
        # Test des imports critiques
        print("📦 Vérification des dépendances...")
        
        from PyQt5.QtWidgets import QApplication
        print("✅ PyQt5 disponible")
        
        from config.settings import config
        print(f"✅ Configuration chargée: {config.APP_NAME}")
        
        from database.manager import DatabaseManager
        print("✅ Base de données prête")
        
        # Lancer l'application
        print("🎯 Lancement de l'interface...")
        from src.app import GSlimApp
        
        app = GSlimApp()
        return app.run()
        
    except ImportError as e:
        print(f"❌ Module manquant: {e}")
        print("💡 Installez les dépendances avec: pip install -r requirements.txt")
        return 1
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
