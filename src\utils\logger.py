"""
Système de logging pour l'application GSlim
"""

import logging
import logging.handlers
from pathlib import Path
from datetime import datetime

from config.settings import config

def setup_logger(name: str, level: int = logging.INFO) -> logging.Logger:
    """
    Configurer et retourner un logger
    
    Args:
        name: Nom du logger
        level: Niveau de logging
        
    Returns:
        Logger configuré
    """
    
    # Créer le répertoire de logs s'il n'existe pas
    log_dir = config.ROOT_DIR / "logs"
    log_dir.mkdir(exist_ok=True)
    
    # Créer le logger
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # Éviter la duplication des handlers
    if logger.handlers:
        return logger
    
    # Format des messages
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Handler pour fichier (rotation quotidienne)
    file_handler = logging.handlers.TimedRotatingFileHandler(
        filename=log_dir / "gslim.log",
        when='midnight',
        interval=1,
        backupCount=30,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    # Handler pour la console (seulement en mode debug)
    if config.DEBUG:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.DEBUG)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    # Handler pour les erreurs (fichier séparé)
    error_handler = logging.FileHandler(
        filename=log_dir / "errors.log",
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(formatter)
    logger.addHandler(error_handler)
    
    return logger

def log_user_action(user_id: int, action: str, details: str = None):
    """
    Logger une action utilisateur pour audit
    
    Args:
        user_id: ID de l'utilisateur
        action: Action effectuée
        details: Détails supplémentaires
    """
    audit_logger = logging.getLogger('audit')
    
    if not audit_logger.handlers:
        # Configurer le logger d'audit
        log_dir = config.ROOT_DIR / "logs"
        log_dir.mkdir(exist_ok=True)
        
        handler = logging.handlers.TimedRotatingFileHandler(
            filename=log_dir / "audit.log",
            when='midnight',
            interval=1,
            backupCount=365,  # Garder 1 an d'audit
            encoding='utf-8'
        )
        
        formatter = logging.Formatter(
            '%(asctime)s - USER_ID:%(user_id)s - %(action)s - %(details)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        handler.setFormatter(formatter)
        audit_logger.addHandler(handler)
        audit_logger.setLevel(logging.INFO)
    
    # Logger l'action
    audit_logger.info(
        "",
        extra={
            'user_id': user_id,
            'action': action,
            'details': details or ""
        }
    )
