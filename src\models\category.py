"""
Modèle pour les catégories d'articles
"""

from dataclasses import dataclass
from typing import Optional, Dict, Any
from datetime import datetime


@dataclass
class Category:
    """Modèle pour une catégorie d'articles"""
    
    id: Optional[int] = None
    name: str = ""
    description: str = ""
    parent_id: Optional[int] = None
    is_active: bool = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        """Validation après initialisation"""
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()
    
    def is_valid(self) -> bool:
        """Vérifier si la catégorie est valide"""
        return (
            len(self.name.strip()) >= 2 and
            len(self.name.strip()) <= 100
        )
    
    def get_validation_errors(self) -> list:
        """Obtenir la liste des erreurs de validation"""
        errors = []
        
        if len(self.name.strip()) < 2:
            errors.append("Le nom doit contenir au moins 2 caractères")
        
        if len(self.name.strip()) > 100:
            errors.append("Le nom ne peut pas dépasser 100 caractères")
        
        if self.description and len(self.description) > 500:
            errors.append("La description ne peut pas dépasser 500 caractères")
        
        return errors
    
    def to_dict(self) -> Dict[str, Any]:
        """Convertir en dictionnaire"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'parent_id': self.parent_id,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def to_display_dict(self) -> Dict[str, Any]:
        """Convertir en dictionnaire pour l'affichage"""
        return {
            'ID': self.id or 'Nouveau',
            'Nom': self.name,
            'Description': self.description or 'Aucune',
            'Catégorie Parent': self.parent_id or 'Racine',
            'Statut': 'Actif' if self.is_active else 'Inactif',
            'Créé le': self.created_at.strftime('%d/%m/%Y') if self.created_at else '',
            'Modifié le': self.updated_at.strftime('%d/%m/%Y') if self.updated_at else ''
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Category':
        """Créer une instance depuis un dictionnaire"""
        # Convertir les dates si elles sont des chaînes
        created_at = data.get('created_at')
        if isinstance(created_at, str):
            created_at = datetime.fromisoformat(created_at)
        
        updated_at = data.get('updated_at')
        if isinstance(updated_at, str):
            updated_at = datetime.fromisoformat(updated_at)
        
        return cls(
            id=data.get('id'),
            name=data.get('name', ''),
            description=data.get('description', ''),
            parent_id=data.get('parent_id'),
            is_active=data.get('is_active', True),
            created_at=created_at,
            updated_at=updated_at
        )
    
    def __str__(self) -> str:
        """Représentation en chaîne"""
        return f"Category(id={self.id}, name='{self.name}')"
    
    def __repr__(self) -> str:
        """Représentation pour le débogage"""
        return self.__str__()


class CategoryHierarchy:
    """Classe pour gérer la hiérarchie des catégories"""
    
    def __init__(self, categories: list):
        self.categories = categories
        self._build_hierarchy()
    
    def _build_hierarchy(self):
        """Construire la hiérarchie des catégories"""
        self.root_categories = []
        self.children_map = {}
        
        # Séparer les catégories racines et construire la map des enfants
        for category in self.categories:
            if category.parent_id is None:
                self.root_categories.append(category)
            else:
                if category.parent_id not in self.children_map:
                    self.children_map[category.parent_id] = []
                self.children_map[category.parent_id].append(category)
    
    def get_children(self, category_id: int) -> list:
        """Obtenir les catégories enfants d'une catégorie"""
        return self.children_map.get(category_id, [])
    
    def get_all_descendants(self, category_id: int) -> list:
        """Obtenir tous les descendants d'une catégorie"""
        descendants = []
        children = self.get_children(category_id)
        
        for child in children:
            descendants.append(child)
            descendants.extend(self.get_all_descendants(child.id))
        
        return descendants
    
    def get_path(self, category_id: int) -> list:
        """Obtenir le chemin complet vers une catégorie"""
        path = []
        current_category = None
        
        # Trouver la catégorie
        for category in self.categories:
            if category.id == category_id:
                current_category = category
                break
        
        if not current_category:
            return path
        
        # Construire le chemin en remontant
        while current_category:
            path.insert(0, current_category)
            if current_category.parent_id:
                # Trouver le parent
                for category in self.categories:
                    if category.id == current_category.parent_id:
                        current_category = category
                        break
                else:
                    break
            else:
                break
        
        return path
    
    def get_breadcrumb(self, category_id: int) -> str:
        """Obtenir le fil d'Ariane d'une catégorie"""
        path = self.get_path(category_id)
        return " > ".join([cat.name for cat in path])
