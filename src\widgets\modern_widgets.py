"""
Widgets modernes personnalisés pour GSlim
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QFrame, QGraphicsDropShadowEffect, QGridLayout, QSizePolicy
)
from PyQt5.QtCore import Qt, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect
from PyQt5.QtGui import QFont, QPixmap, QPainter, QColor, QPen, QBrush

try:
    from qfluentwidgets import (
        CardWidget, IconWidget, FluentIcon, PushButton,
        BodyLabel, CaptionLabel, StrongBodyLabel
    )
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False


class ModernStatCard(QWidget):
    """Carte de statistique moderne avec animations"""
    
    clicked = pyqtSignal()
    
    def __init__(self, title: str, value: str, subtitle: str = "", 
                 card_type: str = "default", icon: str = None):
        super().__init__()
        self.card_type = card_type
        self.setup_ui(title, value, subtitle, icon)
        self.setup_animations()
    
    def setup_ui(self, title: str, value: str, subtitle: str, icon: str):
        """Configurer l'interface utilisateur"""
        self.setFixedSize(280, 140)
        self.setCursor(Qt.PointingHandCursor)
        
        # Appliquer la classe CSS selon le type
        if self.card_type == "primary":
            self.setProperty("class", "stat-card-primary")
        elif self.card_type == "success":
            self.setProperty("class", "stat-card-success")
        elif self.card_type == "warning":
            self.setProperty("class", "stat-card-warning")
        elif self.card_type == "error":
            self.setProperty("class", "stat-card-error")
        else:
            self.setProperty("class", "stat-card")
        
        # Layout principal
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 16, 20, 16)
        layout.setSpacing(8)
        
        # Header avec icône et titre
        header_layout = QHBoxLayout()
        header_layout.setSpacing(12)
        
        # Icône (si disponible)
        if icon and FLUENT_AVAILABLE:
            icon_widget = IconWidget(getattr(FluentIcon, icon, FluentIcon.INFO))
            icon_widget.setFixedSize(24, 24)
            header_layout.addWidget(icon_widget)
        
        # Titre
        if FLUENT_AVAILABLE:
            title_label = CaptionLabel(title)
        else:
            title_label = QLabel(title)
            title_label.setProperty("class", "stat-title")
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
        # Valeur principale
        if FLUENT_AVAILABLE:
            value_label = StrongBodyLabel(value)
            value_label.setStyleSheet("font-size: 28px; font-weight: 700;")
        else:
            value_label = QLabel(value)
            value_label.setProperty("class", "stat-value")
        
        layout.addWidget(value_label)
        
        # Sous-titre (si fourni)
        if subtitle:
            if FLUENT_AVAILABLE:
                subtitle_label = CaptionLabel(subtitle)
            else:
                subtitle_label = QLabel(subtitle)
                subtitle_label.setProperty("class", "stat-subtitle")
            
            layout.addWidget(subtitle_label)
        
        layout.addStretch()
        self.setLayout(layout)
        
        # Effet d'ombre
        self.shadow_effect = QGraphicsDropShadowEffect()
        self.shadow_effect.setBlurRadius(15)
        self.shadow_effect.setXOffset(0)
        self.shadow_effect.setYOffset(4)
        self.shadow_effect.setColor(QColor(0, 0, 0, 30))
        self.setGraphicsEffect(self.shadow_effect)
    
    def setup_animations(self):
        """Configurer les animations"""
        self.animation = QPropertyAnimation(self, b"geometry")
        self.animation.setDuration(200)
        self.animation.setEasingCurve(QEasingCurve.OutCubic)
    
    def enterEvent(self, event):
        """Animation au survol"""
        current_rect = self.geometry()
        new_rect = QRect(
            current_rect.x(),
            current_rect.y() - 2,
            current_rect.width(),
            current_rect.height()
        )
        
        self.animation.setStartValue(current_rect)
        self.animation.setEndValue(new_rect)
        self.animation.start()
        
        # Augmenter l'ombre
        self.shadow_effect.setBlurRadius(20)
        self.shadow_effect.setYOffset(6)
        self.shadow_effect.setColor(QColor(0, 0, 0, 50))
        
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """Animation à la sortie du survol"""
        current_rect = self.geometry()
        new_rect = QRect(
            current_rect.x(),
            current_rect.y() + 2,
            current_rect.width(),
            current_rect.height()
        )
        
        self.animation.setStartValue(current_rect)
        self.animation.setEndValue(new_rect)
        self.animation.start()
        
        # Réduire l'ombre
        self.shadow_effect.setBlurRadius(15)
        self.shadow_effect.setYOffset(4)
        self.shadow_effect.setColor(QColor(0, 0, 0, 30))
        
        super().leaveEvent(event)
    
    def mousePressEvent(self, event):
        """Gérer le clic"""
        if event.button() == Qt.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)


class ModernActionCard(QWidget):
    """Carte d'action moderne avec boutons"""
    
    def __init__(self, title: str, description: str, actions: list):
        super().__init__()
        self.setup_ui(title, description, actions)
    
    def setup_ui(self, title: str, description: str, actions: list):
        """Configurer l'interface utilisateur"""
        self.setProperty("class", "card")
        
        layout = QVBoxLayout()
        layout.setContentsMargins(24, 20, 24, 20)
        layout.setSpacing(16)
        
        # Titre
        if FLUENT_AVAILABLE:
            title_label = StrongBodyLabel(title)
        else:
            title_label = QLabel(title)
            title_label.setProperty("class", "subtitle")
        
        layout.addWidget(title_label)
        
        # Description
        if FLUENT_AVAILABLE:
            desc_label = BodyLabel(description)
        else:
            desc_label = QLabel(description)
            desc_label.setWordWrap(True)
            desc_label.setStyleSheet("color: #64748b; line-height: 1.5;")
        
        layout.addWidget(desc_label)
        
        # Boutons d'action
        if actions:
            buttons_layout = QHBoxLayout()
            buttons_layout.setSpacing(12)
            
            for action in actions:
                if FLUENT_AVAILABLE:
                    btn = PushButton(action['text'])
                    if 'icon' in action:
                        btn.setIcon(getattr(FluentIcon, action['icon'], FluentIcon.ACCEPT))
                else:
                    btn = QPushButton(action['text'])
                
                if 'style' in action:
                    btn.setProperty("class", action['style'])
                
                if 'callback' in action:
                    btn.clicked.connect(action['callback'])
                
                buttons_layout.addWidget(btn)
            
            buttons_layout.addStretch()
            layout.addLayout(buttons_layout)
        
        self.setLayout(layout)


class ModernProgressCard(QWidget):
    """Carte de progression moderne"""
    
    def __init__(self, title: str, current: int, total: int, color: str = "#2563eb"):
        super().__init__()
        self.current = current
        self.total = total
        self.color = color
        self.setup_ui(title)
    
    def setup_ui(self, title: str):
        """Configurer l'interface utilisateur"""
        self.setProperty("class", "card")
        self.setFixedHeight(120)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 16, 20, 16)
        layout.setSpacing(12)
        
        # Header avec titre et pourcentage
        header_layout = QHBoxLayout()
        
        if FLUENT_AVAILABLE:
            title_label = BodyLabel(title)
        else:
            title_label = QLabel(title)
            title_label.setStyleSheet("font-weight: 600; color: #1e293b;")
        
        percentage = int((self.current / self.total * 100)) if self.total > 0 else 0
        
        if FLUENT_AVAILABLE:
            percent_label = CaptionLabel(f"{percentage}%")
        else:
            percent_label = QLabel(f"{percentage}%")
            percent_label.setStyleSheet("color: #64748b; font-weight: 500;")
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(percent_label)
        
        layout.addLayout(header_layout)
        
        # Barre de progression
        progress_container = QWidget()
        progress_container.setFixedHeight(8)
        progress_container.setStyleSheet(f"""
            QWidget {{
                background-color: #e2e8f0;
                border-radius: 4px;
            }}
        """)
        
        # Barre de progression remplie
        progress_fill = QWidget(progress_container)
        fill_width = int((self.current / self.total * progress_container.width())) if self.total > 0 else 0
        progress_fill.setGeometry(0, 0, fill_width, 8)
        progress_fill.setStyleSheet(f"""
            QWidget {{
                background-color: {self.color};
                border-radius: 4px;
            }}
        """)
        
        layout.addWidget(progress_container)
        
        # Texte de progression
        progress_text = f"{self.current} / {self.total}"
        if FLUENT_AVAILABLE:
            progress_label = CaptionLabel(progress_text)
        else:
            progress_label = QLabel(progress_text)
            progress_label.setStyleSheet("color: #64748b; font-size: 12px;")
        
        layout.addWidget(progress_label)
        
        self.setLayout(layout)


class ModernAlertCard(QWidget):
    """Carte d'alerte moderne"""
    
    def __init__(self, title: str, message: str, alert_type: str = "info"):
        super().__init__()
        self.alert_type = alert_type
        self.setup_ui(title, message)
    
    def setup_ui(self, title: str, message: str):
        """Configurer l'interface utilisateur"""
        self.setProperty("class", "card")
        
        # Couleurs selon le type d'alerte
        colors = {
            "info": {"bg": "#dbeafe", "border": "#3b82f6", "text": "#1e40af"},
            "success": {"bg": "#dcfce7", "border": "#10b981", "text": "#065f46"},
            "warning": {"bg": "#fef3c7", "border": "#f59e0b", "text": "#92400e"},
            "error": {"bg": "#fee2e2", "border": "#ef4444", "text": "#991b1b"}
        }
        
        color_scheme = colors.get(self.alert_type, colors["info"])
        
        self.setStyleSheet(f"""
            QWidget[class="card"] {{
                background-color: {color_scheme["bg"]};
                border-left: 4px solid {color_scheme["border"]};
                border-radius: 8px;
            }}
        """)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(16, 12, 16, 12)
        layout.setSpacing(8)
        
        # Titre
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            font-weight: 600;
            color: {color_scheme["text"]};
            font-size: 14px;
        """)
        layout.addWidget(title_label)
        
        # Message
        message_label = QLabel(message)
        message_label.setWordWrap(True)
        message_label.setStyleSheet(f"""
            color: {color_scheme["text"]};
            font-size: 13px;
            line-height: 1.4;
        """)
        layout.addWidget(message_label)
        
        self.setLayout(layout)
