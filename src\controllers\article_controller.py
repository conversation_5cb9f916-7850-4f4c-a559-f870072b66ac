"""
Contrôleur pour la gestion des articles
"""

from typing import Dict, Any, List, Optional, Tuple
from decimal import Decimal
import csv
import io

from .base import BaseController, CRUDResult
from models.article import Article, create_article_from_dict
from models.base import ValidationError


class ArticleController(BaseController):
    """Contrôleur pour la gestion des articles"""
    
    def get_table_name(self) -> str:
        """Nom de la table des articles"""
        return "articles"
    
    def _get_searchable_fields(self) -> List[str]:
        """Champs recherchables pour les articles"""
        return ['name', 'reference', 'description', 'barcode']
    
    def _get_default_order(self) -> str:
        """Ordre par défaut pour les articles"""
        return "name ASC"
    
    def _validate_data(self, data: Dict[str, Any], is_update: bool = False) -> Tuple[bool, str]:
        """Valider les données d'article"""
        try:
            # Créer un objet Article pour validation
            article = Article(**data)
            
            if not article.validate():
                errors = article.get_error_messages()
                return False, "; ".join(errors)
            
            # Vérifications supplémentaires
            if not is_update or 'reference' in data:
                # Vérifier l'unicité de la référence
                if self._is_reference_duplicate(data.get('reference'), data.get('id')):
                    return False, "Cette référence existe déjà"
            
            return True, "Validation réussie"
            
        except Exception as e:
            return False, f"Erreur de validation: {e}"
    
    def _is_reference_duplicate(self, reference: str, exclude_id: int = None) -> bool:
        """Vérifier si une référence existe déjà"""
        try:
            query = "SELECT id FROM articles WHERE reference = ?"
            params = [reference]
            
            if exclude_id:
                query += " AND id != ?"
                params.append(exclude_id)
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                return cursor.fetchone() is not None
                
        except Exception as e:
            self.logger.error(f"Erreur lors de la vérification de référence: {e}")
            return False
    
    def _check_delete_constraints(self, record_id: int) -> Tuple[bool, str]:
        """Vérifier les contraintes avant suppression d'un article"""
        try:
            # Vérifier s'il y a des mouvements de stock
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) as count FROM stock_movements WHERE article_id = ?", (record_id,))
                movement_count = cursor.fetchone()['count']
                
                if movement_count > 0:
                    return False, f"Impossible de supprimer: {movement_count} mouvement(s) de stock associé(s)"
            
            return True, "Suppression autorisée"
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la vérification des contraintes: {e}")
            return False, "Erreur lors de la vérification"
    
    # Méthodes spécifiques aux articles
    def create_article(self, article_data: Dict[str, Any]) -> CRUDResult:
        """Créer un nouvel article"""
        try:
            # Valider avec le modèle Article
            article = create_article_from_dict(article_data)
            
            # Créer en base
            success, result = self.create(article.to_dict())
            
            if success:
                # Récupérer l'article créé
                created_article = self.get_by_id(result)
                return CRUDResult(True, created_article, f"Article '{article_data.get('name')}' créé avec succès")
            else:
                return CRUDResult(False, None, f"Erreur lors de la création: {result}")
                
        except ValidationError as e:
            return CRUDResult(False, None, "Données invalides", e.errors)
        except Exception as e:
            self.logger.error(f"Erreur lors de la création d'article: {e}")
            return CRUDResult(False, None, f"Erreur inattendue: {e}")
    
    def update_article(self, article_id: int, article_data: Dict[str, Any]) -> CRUDResult:
        """Mettre à jour un article"""
        try:
            # Récupérer l'article existant
            existing_article = self.get_by_id(article_id)
            if not existing_article:
                return CRUDResult(False, None, "Article non trouvé")
            
            # Fusionner les données
            updated_data = existing_article.copy()
            updated_data.update(article_data)
            
            # Valider avec le modèle Article
            article = create_article_from_dict(updated_data)
            
            # Mettre à jour en base
            success, message = self.update(article_id, article_data)
            
            if success:
                # Récupérer l'article mis à jour
                updated_article = self.get_by_id(article_id)
                return CRUDResult(True, updated_article, f"Article '{updated_article.get('name')}' mis à jour")
            else:
                return CRUDResult(False, None, f"Erreur lors de la mise à jour: {message}")
                
        except ValidationError as e:
            return CRUDResult(False, None, "Données invalides", e.errors)
        except Exception as e:
            self.logger.error(f"Erreur lors de la mise à jour d'article: {e}")
            return CRUDResult(False, None, f"Erreur inattendue: {e}")
    
    def get_article_with_details(self, article_id: int) -> Optional[Dict[str, Any]]:
        """Récupérer un article avec ses détails calculés"""
        try:
            article_data = self.get_by_id(article_id)
            if not article_data:
                return None
            
            # Créer un objet Article pour les calculs
            article = Article(**article_data)
            
            # Retourner les données enrichies
            return article.to_display_dict()
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des détails: {e}")
            return None
    
    def get_articles_with_details(self, filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Récupérer tous les articles avec leurs détails calculés"""
        try:
            articles_data = self.get_all(filters)
            articles_with_details = []
            
            for article_data in articles_data:
                article = Article(**article_data)
                articles_with_details.append(article.to_display_dict())
            
            return articles_with_details
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des articles: {e}")
            return []
    
    def get_low_stock_articles(self) -> List[Dict[str, Any]]:
        """Récupérer les articles en stock bas"""
        try:
            query = """
                SELECT * FROM articles 
                WHERE quantity_in_stock <= minimum_stock 
                AND is_active = 1
                ORDER BY (quantity_in_stock - minimum_stock) ASC
            """
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query)
                rows = cursor.fetchall()
                
                articles_with_details = []
                for row in rows:
                    article = Article(**dict(row))
                    articles_with_details.append(article.to_display_dict())
                
                return articles_with_details
                
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des articles en stock bas: {e}")
            return []
    
    def get_out_of_stock_articles(self) -> List[Dict[str, Any]]:
        """Récupérer les articles en rupture de stock"""
        try:
            query = """
                SELECT * FROM articles 
                WHERE quantity_in_stock = 0 
                AND is_active = 1
                ORDER BY name ASC
            """
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query)
                rows = cursor.fetchall()
                
                articles_with_details = []
                for row in rows:
                    article = Article(**dict(row))
                    articles_with_details.append(article.to_display_dict())
                
                return articles_with_details
                
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des articles en rupture: {e}")
            return []
    
    def update_stock(self, article_id: int, new_quantity: int, reason: str = "Ajustement manuel") -> CRUDResult:
        """Mettre à jour le stock d'un article"""
        try:
            article_data = self.get_by_id(article_id)
            if not article_data:
                return CRUDResult(False, None, "Article non trouvé")
            
            old_quantity = article_data['quantity_in_stock']
            
            # Mettre à jour la quantité
            success, message = self.update(article_id, {
                'quantity_in_stock': new_quantity
            })
            
            if success:
                # Créer un mouvement de stock
                from .stock_movement_controller import StockMovementController
                movement_controller = StockMovementController(self.db_manager)
                
                movement_data = {
                    'article_id': article_id,
                    'article_name': article_data['name'],
                    'article_reference': article_data['reference'],
                    'movement_type': 'Ajustement',
                    'movement_reason': reason,
                    'quantity': abs(new_quantity - old_quantity),
                    'previous_quantity': old_quantity,
                    'new_quantity': new_quantity,
                    'user_id': 1,  # TODO: Récupérer l'utilisateur actuel
                    'user_name': 'Système'
                }
                
                movement_controller.create_movement(movement_data)
                
                return CRUDResult(True, new_quantity, f"Stock mis à jour: {old_quantity} → {new_quantity}")
            else:
                return CRUDResult(False, None, f"Erreur lors de la mise à jour du stock: {message}")
                
        except Exception as e:
            self.logger.error(f"Erreur lors de la mise à jour du stock: {e}")
            return CRUDResult(False, None, f"Erreur inattendue: {e}")
    
    def search_articles(self, search_term: str, filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Rechercher des articles avec filtres avancés"""
        try:
            # Recherche de base
            articles = self.search(search_term)
            
            # Appliquer les filtres supplémentaires
            if filters:
                filtered_articles = []
                for article_data in articles:
                    article = Article(**article_data)
                    
                    # Filtrer par catégorie
                    if filters.get('category_id') and article_data.get('category_id') != filters['category_id']:
                        continue
                    
                    # Filtrer par fournisseur
                    if filters.get('supplier_id') and article_data.get('supplier_id') != filters['supplier_id']:
                        continue
                    
                    # Filtrer par statut de stock
                    if filters.get('stock_status'):
                        if filters['stock_status'] == 'low' and not article.is_low_stock():
                            continue
                        elif filters['stock_status'] == 'out' and not article.is_out_of_stock():
                            continue
                        elif filters['stock_status'] == 'normal' and (article.is_low_stock() or article.is_out_of_stock()):
                            continue
                    
                    # Filtrer par statut actif
                    if filters.get('is_active') is not None and article_data.get('is_active') != filters['is_active']:
                        continue
                    
                    filtered_articles.append(article.to_display_dict())
                
                return filtered_articles
            else:
                # Retourner avec détails calculés
                return [Article(**data).to_display_dict() for data in articles]
                
        except Exception as e:
            self.logger.error(f"Erreur lors de la recherche d'articles: {e}")
            return []
    
    def get_stock_statistics(self) -> Dict[str, Any]:
        """Obtenir les statistiques de stock"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()

            # Statistiques générales
            cursor.execute("SELECT COUNT(*) as total FROM articles WHERE is_active = 1")
            total_articles = cursor.fetchone()['total']

            cursor.execute("SELECT COUNT(*) as count FROM articles WHERE quantity_in_stock = 0 AND is_active = 1")
            out_of_stock = cursor.fetchone()['count']

            cursor.execute("SELECT COUNT(*) as count FROM articles WHERE quantity_in_stock <= minimum_stock AND is_active = 1")
            low_stock = cursor.fetchone()['count']

            cursor.execute("SELECT SUM(quantity_in_stock * unit_price) as value FROM articles WHERE is_active = 1")
            total_value = cursor.fetchone()['value'] or 0

            cursor.execute("SELECT SUM(quantity_in_stock) as quantity FROM articles WHERE is_active = 1")
            total_quantity = cursor.fetchone()['quantity'] or 0

            return {
                'total_articles': total_articles,
                'out_of_stock': out_of_stock,
                'low_stock': low_stock,
                'normal_stock': total_articles - out_of_stock - low_stock,
                'total_value': float(total_value),
                'total_quantity': total_quantity,
                    'out_of_stock_percentage': (out_of_stock / total_articles * 100) if total_articles > 0 else 0,
                    'low_stock_percentage': (low_stock / total_articles * 100) if total_articles > 0 else 0
                }
                
        except Exception as e:
            self.logger.error(f"Erreur lors du calcul des statistiques: {e}")
            return {
                'total_articles': 0,
                'out_of_stock': 0,
                'low_stock': 0,
                'normal_stock': 0,
                'total_value': 0.0,
                'total_quantity': 0,
                'out_of_stock_percentage': 0.0,
                'low_stock_percentage': 0.0
            }
