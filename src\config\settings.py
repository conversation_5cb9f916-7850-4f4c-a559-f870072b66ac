"""
Configuration de l'application GSlim
"""

import os
from pathlib import Path

# Essayer de charger dotenv, sinon utiliser les valeurs par défaut
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("python-dotenv non installé, utilisation des valeurs par défaut")

class Config:
    """Configuration de base de l'application"""

    # Répertoire racine du projet
    ROOT_DIR = Path(__file__).parent.parent.parent

    # Application
    APP_NAME = os.getenv("APP_NAME", "GSlim")
    APP_VERSION = os.getenv("APP_VERSION", "1.0.0")
    DEBUG = os.getenv("DEBUG", "False").lower() == "true"

    # Base de données
    DATABASE_PATH = ROOT_DIR / os.getenv("DATABASE_PATH", "data/gslim.db")
    
    # Sécurité
    SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-change-this")
    BCRYPT_ROUNDS = int(os.getenv("BCRYPT_ROUNDS", "12"))
    
    # Interface
    DEFAULT_THEME = os.getenv("DEFAULT_THEME", "darkly")
    LANGUAGE = os.getenv("LANGUAGE", "fr")
    
    # Répertoires
    REPORTS_DIR = ROOT_DIR / os.getenv("REPORTS_DIR", "reports")
    TEMP_DIR = ROOT_DIR / os.getenv("TEMP_DIR", "temp")
    DATA_DIR = ROOT_DIR / "data"
    
    # Email (optionnel)
    SMTP_SERVER = os.getenv("SMTP_SERVER")
    SMTP_PORT = int(os.getenv("SMTP_PORT", "587"))
    SMTP_USERNAME = os.getenv("SMTP_USERNAME")
    SMTP_PASSWORD = os.getenv("SMTP_PASSWORD")
    SMTP_USE_TLS = os.getenv("SMTP_USE_TLS", "True").lower() == "true"
    
    # Interface utilisateur
    WINDOW_WIDTH = 1200
    WINDOW_HEIGHT = 800
    MIN_WIDTH = 800
    MIN_HEIGHT = 600
    
    # Pagination
    DEFAULT_PAGE_SIZE = 50
    MAX_PAGE_SIZE = 200
    
    @classmethod
    def ensure_directories(cls):
        """Créer les répertoires nécessaires s'ils n'existent pas"""
        directories = [
            cls.DATA_DIR,
            cls.REPORTS_DIR,
            cls.TEMP_DIR,
            cls.DATABASE_PATH.parent
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)

# Instance de configuration globale
config = Config()

# Créer les répertoires au chargement du module
config.ensure_directories()
