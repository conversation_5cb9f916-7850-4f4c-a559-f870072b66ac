# 📖 Guide d'utilisation - GSlim

## 🚀 Démarrage rapide

### 1. Installation des dépendances

```bash
# Installer toutes les dépendances
pip install -r requirements.txt

# Ou installer manuellement les principales :
pip install PyQt5 python-dotenv bcrypt
```

### 2. Lancement de l'application

```bash
# Méthode 1 : Script principal
python main.py

# Méthode 2 : Script de démarrage avec diagnostics
python start_app.py

# Méthode 3 : Tests complets
python test_app.py
```

### 3. Connexion

**Identifiants par défaut :**
- **Nom d'utilisateur :** `admin`
- **Mot de passe :** `admin123`

## 🎨 Interface utilisateur

### Thèmes disponibles
- **Thème sombre** (par défaut) : Interface moderne avec fond noir
- **Thème clair** : Interface avec fond blanc
- **Basculement** : Bouton "Changer thème" dans l'en-tête

### Navigation
- **<PERSON><PERSON> la<PERSON>** avec icônes (si Fluent Widgets disponible)
- **Modules disponibles :**
  - 📊 Tableau de bord
  - 📦 Articles
  - 🏢 Fournisseurs  
  - 📈 Mouvements de stock
  - 🛒 Commandes
  - 📋 Rapports
  - ⚙️ Paramètres

## 🗄️ Base de données

### Localisation
- **Fichier :** `data/gslim.db` (SQLite)
- **Création automatique** au premier lancement

### Tables créées
- `users` - Utilisateurs et authentification
- `categories` - Catégories d'articles
- `suppliers` - Fournisseurs
- `articles` - Articles en stock
- `stock_movements` - Mouvements de stock
- `orders` - Commandes
- `order_items` - Détails des commandes
- `settings` - Paramètres application

## 🔧 Configuration

### Fichier .env
Copiez `.env.example` vers `.env` et modifiez selon vos besoins :

```env
# Base de données
DATABASE_PATH=data/gslim.db

# Application
APP_NAME=GSlim
APP_VERSION=1.0.0
DEBUG=True

# Interface
DEFAULT_THEME=darkly
LANGUAGE=fr
```

### Répertoires créés automatiquement
- `data/` - Base de données
- `reports/` - Rapports générés
- `temp/` - Fichiers temporaires

## 🎯 Fonctionnalités actuelles

### ✅ Implémenté
- 🔐 Système de connexion sécurisé (bcrypt)
- 🎨 Interface moderne PyQt5 + CSS
- 🌓 Thèmes clair/sombre
- 📱 Design responsive
- 🗄️ Base de données SQLite
- 📝 Système de logging
- 🔄 Navigation entre modules

### 🚧 En développement
- 📦 CRUD Articles complet
- 🏢 Gestion fournisseurs
- 📊 Tableau de bord avec statistiques
- 📈 Graphiques et analyses
- 📋 Génération de rapports PDF/Excel
- 📤 Import/Export CSV

## 🛠️ Dépannage

### Erreur "Module not found"
```bash
# Installer les dépendances manquantes
pip install PyQt5 python-dotenv bcrypt pandas matplotlib reportlab
```

### Erreur de base de données
- Vérifiez que le répertoire `data/` existe
- Supprimez `data/gslim.db` pour recréer la base

### Interface ne s'affiche pas
- Vérifiez que PyQt5 est correctement installé
- Testez avec : `python test_pyqt5.py`

### Fluent Widgets non disponible
```bash
# Installation optionnelle pour interface avancée
pip install PyQt-Fluent-Widgets
```

## 📞 Support

### Logs
- Fichiers de log dans le répertoire de l'application
- Mode debug activable dans `.env` : `DEBUG=True`

### Structure du projet
```
GSlim/
├── main.py              # Point d'entrée
├── start_app.py         # Démarrage avec diagnostics
├── test_app.py          # Tests complets
├── requirements.txt     # Dépendances
├── .env                 # Configuration
├── src/
│   ├── app.py          # Application principale
│   ├── config/         # Configuration
│   ├── database/       # Gestion BDD
│   ├── models/         # Modèles de données
│   ├── views/          # Interfaces utilisateur
│   ├── controllers/    # Logique métier
│   ├── styles/         # Thèmes CSS
│   └── utils/          # Utilitaires
├── data/               # Base de données
├── reports/            # Rapports générés
└── temp/               # Fichiers temporaires
```

## 🎉 Prochaines étapes

1. **Développer les modules métier** (Articles, Fournisseurs, etc.)
2. **Ajouter les graphiques** avec matplotlib/plotly
3. **Implémenter l'import/export** CSV/Excel
4. **Créer le système de rapports** PDF
5. **Ajouter les notifications** pour stock bas

---

**Version :** 1.0.0  
**Dernière mise à jour :** 2024  
**Technologies :** PyQt5, SQLite, CSS, Fluent Design
