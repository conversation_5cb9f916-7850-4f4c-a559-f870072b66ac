"""
Modèle de base pour tous les modèles de données GSlim
"""

from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, Any, Optional, List
import json

from utils.logger import setup_logger


class BaseModel(ABC):
    """Classe de base pour tous les modèles"""
    
    def __init__(self, **kwargs):
        """Initialiser le modèle avec les données"""
        self.logger = setup_logger(self.__class__.__name__)
        self._data = {}
        self._errors = {}
        self._is_valid = True
        
        # Définir les champs par défaut
        self._set_defaults()
        
        # Charger les données fournies
        for key, value in kwargs.items():
            if hasattr(self, f"set_{key}"):
                getattr(self, f"set_{key}")(value)
            else:
                self._data[key] = value
    
    @abstractmethod
    def _set_defaults(self):
        """Définir les valeurs par défaut (à implémenter dans chaque modèle)"""
        pass
    
    @abstractmethod
    def get_validation_rules(self) -> Dict[str, Dict]:
        """Retourner les règles de validation (à implémenter dans chaque modèle)"""
        pass
    
    def validate(self) -> bool:
        """Valider le modèle selon les règles définies"""
        self._errors = {}
        self._is_valid = True
        
        rules = self.get_validation_rules()
        
        for field, rule_set in rules.items():
            value = self._data.get(field)
            field_errors = []
            
            # Vérifier si le champ est requis
            if rule_set.get('required', False) and (value is None or value == ''):
                field_errors.append(f"Le champ {field} est requis")
            
            # Vérifier le type
            if value is not None and 'type' in rule_set:
                expected_type = rule_set['type']
                if not isinstance(value, expected_type):
                    field_errors.append(f"Le champ {field} doit être de type {expected_type.__name__}")
            
            # Vérifier la longueur minimale
            if value and 'min_length' in rule_set:
                if len(str(value)) < rule_set['min_length']:
                    field_errors.append(f"Le champ {field} doit contenir au moins {rule_set['min_length']} caractères")
            
            # Vérifier la longueur maximale
            if value and 'max_length' in rule_set:
                if len(str(value)) > rule_set['max_length']:
                    field_errors.append(f"Le champ {field} ne peut pas dépasser {rule_set['max_length']} caractères")
            
            # Vérifier la valeur minimale
            if value is not None and 'min_value' in rule_set:
                if float(value) < rule_set['min_value']:
                    field_errors.append(f"Le champ {field} doit être supérieur ou égal à {rule_set['min_value']}")
            
            # Vérifier la valeur maximale
            if value is not None and 'max_value' in rule_set:
                if float(value) > rule_set['max_value']:
                    field_errors.append(f"Le champ {field} doit être inférieur ou égal à {rule_set['max_value']}")
            
            # Validation personnalisée
            if 'validator' in rule_set and value is not None:
                try:
                    if not rule_set['validator'](value):
                        field_errors.append(rule_set.get('validator_message', f"Valeur invalide pour {field}"))
                except Exception as e:
                    field_errors.append(f"Erreur de validation pour {field}: {e}")
            
            if field_errors:
                self._errors[field] = field_errors
                self._is_valid = False
        
        return self._is_valid
    
    def is_valid(self) -> bool:
        """Vérifier si le modèle est valide"""
        return self._is_valid
    
    def get_errors(self) -> Dict[str, List[str]]:
        """Retourner les erreurs de validation"""
        return self._errors
    
    def get_error_messages(self) -> List[str]:
        """Retourner tous les messages d'erreur sous forme de liste"""
        messages = []
        for field_errors in self._errors.values():
            messages.extend(field_errors)
        return messages
    
    def to_dict(self) -> Dict[str, Any]:
        """Convertir le modèle en dictionnaire"""
        return self._data.copy()
    
    def to_json(self) -> str:
        """Convertir le modèle en JSON"""
        def json_serializer(obj):
            if isinstance(obj, datetime):
                return obj.isoformat()
            raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
        
        return json.dumps(self._data, default=json_serializer, ensure_ascii=False, indent=2)
    
    def get(self, key: str, default=None):
        """Obtenir une valeur"""
        return self._data.get(key, default)
    
    def set(self, key: str, value: Any):
        """Définir une valeur"""
        self._data[key] = value
    
    def update(self, **kwargs):
        """Mettre à jour plusieurs valeurs"""
        for key, value in kwargs.items():
            self.set(key, value)
    
    def __getitem__(self, key):
        """Permettre l'accès par index"""
        return self._data[key]
    
    def __setitem__(self, key, value):
        """Permettre l'assignation par index"""
        self._data[key] = value
    
    def __contains__(self, key):
        """Permettre l'utilisation de 'in'"""
        return key in self._data
    
    def __str__(self):
        """Représentation string du modèle"""
        return f"{self.__class__.__name__}({self._data})"
    
    def __repr__(self):
        """Représentation pour le débogage"""
        return self.__str__()


class TimestampMixin:
    """Mixin pour ajouter des timestamps automatiques"""
    
    def _set_timestamp_defaults(self):
        """Définir les timestamps par défaut"""
        now = datetime.now()
        if 'created_at' not in self._data:
            self._data['created_at'] = now
        if 'updated_at' not in self._data:
            self._data['updated_at'] = now
    
    def touch(self):
        """Mettre à jour le timestamp updated_at"""
        self._data['updated_at'] = datetime.now()


class ValidationError(Exception):
    """Exception pour les erreurs de validation"""
    
    def __init__(self, errors: Dict[str, List[str]]):
        self.errors = errors
        messages = []
        for field_errors in errors.values():
            messages.extend(field_errors)
        super().__init__("; ".join(messages))


def validate_email(email: str) -> bool:
    """Valider une adresse email"""
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None


def validate_phone(phone: str) -> bool:
    """Valider un numéro de téléphone"""
    import re
    # Accepter différents formats de téléphone
    pattern = r'^[\+]?[0-9\s\-\(\)\.]{8,20}$'
    return re.match(pattern, phone) is not None


def validate_positive_number(value) -> bool:
    """Valider qu'un nombre est positif"""
    try:
        return float(value) >= 0
    except (ValueError, TypeError):
        return False


def validate_non_empty_string(value) -> bool:
    """Valider qu'une chaîne n'est pas vide"""
    return isinstance(value, str) and value.strip() != ""
