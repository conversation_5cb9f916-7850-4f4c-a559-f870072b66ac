"""
Fenêtre de gestion des fournisseurs
Interface complète pour CRUD fournisseurs avec recherche et filtrage
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QTableWidget, QTableWidgetItem, QLineEdit, QComboBox,
    QFrame, QSplitter, QHeaderView, QAbstractItemView,
    QMessageBox, QDialog, QFormLayout, QSpinBox, QDoubleSpinBox,
    QTextEdit, QCheckBox, QGroupBox, QGridLayout, QTabWidget
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QColor, QPalette

try:
    from qfluentwidgets import (
        PushButton, LineEdit, ComboBox, TableWidget,
        SearchLineEdit, CardWidget, TitleLabel, CaptionLabel,
        FluentIcon, InfoBar, InfoBarPosition, RatingControl
    )
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False

from controllers.supplier_controller import SupplierController
from controllers.article_controller import ArticleController
from models.supplier import get_payment_methods, get_supplier_categories
from utils.logger import setup_logger


class SuppliersWindow(QWidget):
    """Fenêtre de gestion des fournisseurs"""
    
    supplier_selected = pyqtSignal(dict)
    
    def __init__(self, app_instance):
        super().__init__()
        self.app_instance = app_instance
        self.logger = setup_logger(__name__)
        self.controller = SupplierController(app_instance.get_database_manager())
        self.article_controller = ArticleController(app_instance.get_database_manager())
        
        # Variables d'état
        self.current_suppliers = []
        self.selected_supplier = None
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self._perform_search)
        
        self._init_ui()
        self._connect_signals()
        self._load_suppliers()
    
    def _init_ui(self):
        """Initialiser l'interface utilisateur"""
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # En-tête
        self._create_header(layout)
        
        # Barre d'outils et recherche
        self._create_toolbar(layout)
        
        # Zone principale avec splitter
        self._create_main_area(layout)
        
        # Barre de statut
        self._create_status_bar(layout)
        
        self.setLayout(layout)
    
    def _create_header(self, layout):
        """Créer l'en-tête"""
        header_layout = QHBoxLayout()
        
        if FLUENT_AVAILABLE:
            title = TitleLabel("Gestion des Fournisseurs")
        else:
            title = QLabel("Gestion des Fournisseurs")
            title.setProperty("class", "title")
            font = QFont("Segoe UI", 24, QFont.Bold)
            title.setFont(font)
        
        header_layout.addWidget(title)
        header_layout.addStretch()
        
        # Statistiques rapides
        self._create_stats_cards(header_layout)
        
        layout.addLayout(header_layout)
    
    def _create_stats_cards(self, layout):
        """Créer les cartes de statistiques"""
        # Compter les fournisseurs
        total_suppliers = self.controller.count()
        active_suppliers = self.controller.count({'is_active': True})
        top_suppliers = len(self.controller.get_top_suppliers(5))
        
        # Carte total fournisseurs
        total_card = self._create_stat_card("Fournisseurs", str(total_suppliers), "#0078d4")
        layout.addWidget(total_card)
        
        # Carte fournisseurs actifs
        active_card = self._create_stat_card("Actifs", str(active_suppliers), "#28a745")
        layout.addWidget(active_card)
        
        # Carte top fournisseurs
        top_card = self._create_stat_card("Top rated", str(top_suppliers), "#fd7e14")
        layout.addWidget(top_card)
    
    def _create_stat_card(self, title: str, value: str, color: str):
        """Créer une carte de statistique"""
        if FLUENT_AVAILABLE:
            card = CardWidget()
            card.setFixedSize(120, 80)
        else:
            card = QFrame()
            card.setProperty("class", "card")
            card.setFixedSize(120, 80)
        
        card_layout = QVBoxLayout()
        card_layout.setContentsMargins(10, 10, 10, 10)
        card_layout.setSpacing(5)
        
        # Valeur
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet(f"color: {color}; font-size: 18px; font-weight: bold;")
        card_layout.addWidget(value_label)
        
        # Titre
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #666; font-size: 12px;")
        card_layout.addWidget(title_label)
        
        card.setLayout(card_layout)
        return card
    
    def _create_toolbar(self, layout):
        """Créer la barre d'outils"""
        toolbar_layout = QHBoxLayout()
        
        # Recherche
        if FLUENT_AVAILABLE:
            self.search_input = SearchLineEdit()
            self.search_input.setPlaceholderText("Rechercher un fournisseur...")
        else:
            self.search_input = QLineEdit()
            self.search_input.setPlaceholderText("Rechercher un fournisseur...")
        
        self.search_input.setFixedWidth(300)
        toolbar_layout.addWidget(self.search_input)
        
        # Filtres
        self.category_filter = QComboBox()
        self.category_filter.addItem("Toutes les catégories", None)
        for category in get_supplier_categories():
            self.category_filter.addItem(category, category)
        self.category_filter.setFixedWidth(200)
        toolbar_layout.addWidget(self.category_filter)
        
        self.status_filter = QComboBox()
        self.status_filter.addItems(["Tous les statuts", "Actifs", "Inactifs"])
        self.status_filter.setFixedWidth(150)
        toolbar_layout.addWidget(self.status_filter)
        
        self.rating_filter = QComboBox()
        self.rating_filter.addItems(["Toutes les notes", "5 étoiles", "4+ étoiles", "3+ étoiles", "2+ étoiles"])
        self.rating_filter.setFixedWidth(150)
        toolbar_layout.addWidget(self.rating_filter)
        
        toolbar_layout.addStretch()
        
        # Boutons d'action
        if FLUENT_AVAILABLE:
            self.add_button = PushButton("Nouveau")
            self.add_button.setIcon(FluentIcon.ADD)
            self.edit_button = PushButton("Modifier")
            self.edit_button.setIcon(FluentIcon.EDIT)
            self.delete_button = PushButton("Supprimer")
            self.delete_button.setIcon(FluentIcon.DELETE)
            self.refresh_button = PushButton("Actualiser")
            self.refresh_button.setIcon(FluentIcon.SYNC)
        else:
            self.add_button = QPushButton("Nouveau")
            self.edit_button = QPushButton("Modifier")
            self.delete_button = QPushButton("Supprimer")
            self.refresh_button = QPushButton("Actualiser")
        
        # Désactiver les boutons par défaut
        self.edit_button.setEnabled(False)
        self.delete_button.setEnabled(False)
        
        toolbar_layout.addWidget(self.add_button)
        toolbar_layout.addWidget(self.edit_button)
        toolbar_layout.addWidget(self.delete_button)
        toolbar_layout.addWidget(self.refresh_button)
        
        layout.addLayout(toolbar_layout)
    
    def _create_main_area(self, layout):
        """Créer la zone principale"""
        splitter = QSplitter(Qt.Horizontal)
        
        # Table des fournisseurs
        self._create_suppliers_table(splitter)
        
        # Panneau de détails
        self._create_details_panel(splitter)
        
        # Configuration du splitter
        splitter.setSizes([700, 400])
        splitter.setCollapsible(0, False)
        splitter.setCollapsible(1, True)
        
        layout.addWidget(splitter)
    
    def _create_suppliers_table(self, parent):
        """Créer la table des fournisseurs"""
        if FLUENT_AVAILABLE:
            self.suppliers_table = TableWidget()
        else:
            self.suppliers_table = QTableWidget()
        
        # Configuration de la table
        columns = [
            "Nom", "Contact", "Email", "Téléphone", "Ville", 
            "Note", "Statut", "Articles", "Catégorie"
        ]
        
        self.suppliers_table.setColumnCount(len(columns))
        self.suppliers_table.setHorizontalHeaderLabels(columns)
        
        # Configuration des colonnes
        header = self.suppliers_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # Nom
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Contact
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Email
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Téléphone
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Ville
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Note
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Statut
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # Articles
        
        # Configuration du comportement
        self.suppliers_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.suppliers_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.suppliers_table.setAlternatingRowColors(True)
        self.suppliers_table.setSortingEnabled(True)
        
        parent.addWidget(self.suppliers_table)
    
    def _create_details_panel(self, parent):
        """Créer le panneau de détails"""
        if FLUENT_AVAILABLE:
            details_card = CardWidget()
        else:
            details_card = QFrame()
            details_card.setProperty("class", "card")
        
        details_layout = QVBoxLayout()
        details_layout.setContentsMargins(15, 15, 15, 15)
        details_layout.setSpacing(15)
        
        # Titre
        if FLUENT_AVAILABLE:
            details_title = TitleLabel("Détails du fournisseur")
        else:
            details_title = QLabel("Détails du fournisseur")
            details_title.setProperty("class", "subtitle")
        
        details_layout.addWidget(details_title)
        
        # Onglets pour organiser les informations
        self.details_tabs = QTabWidget()
        
        # Onglet Informations générales
        self.general_tab = QWidget()
        self.general_content = QLabel("Sélectionnez un fournisseur pour voir ses détails")
        self.general_content.setWordWrap(True)
        self.general_content.setAlignment(Qt.AlignTop)
        
        general_layout = QVBoxLayout()
        general_layout.addWidget(self.general_content)
        general_layout.addStretch()
        self.general_tab.setLayout(general_layout)
        
        # Onglet Articles associés
        self.articles_tab = QWidget()
        self.articles_content = QLabel("Aucun article associé")
        self.articles_content.setWordWrap(True)
        self.articles_content.setAlignment(Qt.AlignTop)
        
        articles_layout = QVBoxLayout()
        articles_layout.addWidget(self.articles_content)
        articles_layout.addStretch()
        self.articles_tab.setLayout(articles_layout)
        
        # Ajouter les onglets
        self.details_tabs.addTab(self.general_tab, "Informations")
        self.details_tabs.addTab(self.articles_tab, "Articles")
        
        details_layout.addWidget(self.details_tabs)
        details_card.setLayout(details_layout)
        
        parent.addWidget(details_card)
    
    def _create_status_bar(self, layout):
        """Créer la barre de statut"""
        self.status_label = QLabel("Prêt")
        self.status_label.setProperty("class", "caption")
        layout.addWidget(self.status_label)
    
    def _connect_signals(self):
        """Connecter les signaux"""
        # Recherche avec délai
        self.search_input.textChanged.connect(self._on_search_changed)
        
        # Filtres
        self.category_filter.currentTextChanged.connect(self._apply_filters)
        self.status_filter.currentTextChanged.connect(self._apply_filters)
        self.rating_filter.currentTextChanged.connect(self._apply_filters)
        
        # Boutons
        self.add_button.clicked.connect(self._add_supplier)
        self.edit_button.clicked.connect(self._edit_supplier)
        self.delete_button.clicked.connect(self._delete_supplier)
        self.refresh_button.clicked.connect(self._load_suppliers)
        
        # Table
        self.suppliers_table.itemSelectionChanged.connect(self._on_selection_changed)
        self.suppliers_table.itemDoubleClicked.connect(self._edit_supplier)
    
    def _load_suppliers(self):
        """Charger les fournisseurs"""
        try:
            self.status_label.setText("Chargement des fournisseurs...")
            self.current_suppliers = self.controller.get_suppliers_with_details()
            self._populate_table()
            self._update_stats()
            self.status_label.setText(f"{len(self.current_suppliers)} fournisseur(s) chargé(s)")
            
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des fournisseurs: {e}")
            self._show_error("Erreur", f"Impossible de charger les fournisseurs: {e}")
    
    def _populate_table(self):
        """Remplir la table avec les fournisseurs"""
        self.suppliers_table.setRowCount(len(self.current_suppliers))
        
        for row, supplier in enumerate(self.current_suppliers):
            # Nom
            name_item = QTableWidgetItem(supplier.get('full_name', ''))
            if not supplier.get('is_active'):
                name_item.setForeground(QColor("#999999"))
            self.suppliers_table.setItem(row, 0, name_item)
            
            # Contact
            self.suppliers_table.setItem(row, 1, QTableWidgetItem(supplier.get('contact_person', '')))
            
            # Email
            self.suppliers_table.setItem(row, 2, QTableWidgetItem(supplier.get('email', '')))
            
            # Téléphone
            self.suppliers_table.setItem(row, 3, QTableWidgetItem(supplier.get('phone', '')))
            
            # Ville
            self.suppliers_table.setItem(row, 4, QTableWidgetItem(supplier.get('city', '')))
            
            # Note
            rating_item = QTableWidgetItem(supplier.get('rating_stars', ''))
            rating_item.setTextAlignment(Qt.AlignCenter)
            self.suppliers_table.setItem(row, 5, rating_item)
            
            # Statut
            status_item = QTableWidgetItem(supplier.get('status_text', ''))
            status_color = supplier.get('status_color', '#000000')
            status_item.setForeground(QColor(status_color))
            self.suppliers_table.setItem(row, 6, status_item)
            
            # Articles
            article_count = supplier.get('article_count', 0)
            self.suppliers_table.setItem(row, 7, QTableWidgetItem(str(article_count)))
            
            # Catégorie
            self.suppliers_table.setItem(row, 8, QTableWidgetItem(supplier.get('category', '')))
    
    def _update_stats(self):
        """Mettre à jour les statistiques"""
        # Cette méthode sera appelée pour rafraîchir les cartes de stats
        pass

    def _on_search_changed(self):
        """Gérer le changement de recherche avec délai"""
        self.search_timer.stop()
        self.search_timer.start(500)  # Délai de 500ms

    def _perform_search(self):
        """Effectuer la recherche"""
        search_term = self.search_input.text().strip()

        if search_term:
            self.status_label.setText(f"Recherche: '{search_term}'...")
            filtered_suppliers = []

            for supplier in self.current_suppliers:
                if (search_term.lower() in supplier.get('name', '').lower() or
                    search_term.lower() in supplier.get('company_name', '').lower() or
                    search_term.lower() in supplier.get('contact_person', '').lower() or
                    search_term.lower() in supplier.get('email', '').lower() or
                    search_term.lower() in supplier.get('city', '').lower()):
                    filtered_suppliers.append(supplier)

            self.current_suppliers = filtered_suppliers
            self.status_label.setText(f"{len(filtered_suppliers)} fournisseur(s) trouvé(s)")
        else:
            self._load_suppliers()

        self._populate_table()

    def _apply_filters(self):
        """Appliquer les filtres"""
        # Recharger tous les fournisseurs puis appliquer les filtres
        all_suppliers = self.controller.get_suppliers_with_details()
        filtered_suppliers = []

        category_filter = self.category_filter.currentData()
        status_filter = self.status_filter.currentText()
        rating_filter = self.rating_filter.currentText()

        for supplier in all_suppliers:
            # Filtre catégorie
            if category_filter and supplier.get('category') != category_filter:
                continue

            # Filtre statut
            if status_filter == "Actifs" and not supplier.get('is_active'):
                continue
            elif status_filter == "Inactifs" and supplier.get('is_active'):
                continue

            # Filtre note
            rating = supplier.get('rating', 0)
            if rating_filter == "5 étoiles" and rating != 5:
                continue
            elif rating_filter == "4+ étoiles" and rating < 4:
                continue
            elif rating_filter == "3+ étoiles" and rating < 3:
                continue
            elif rating_filter == "2+ étoiles" and rating < 2:
                continue

            filtered_suppliers.append(supplier)

        self.current_suppliers = filtered_suppliers
        self._populate_table()
        self.status_label.setText(f"{len(filtered_suppliers)} fournisseur(s) après filtrage")

    def _on_selection_changed(self):
        """Gérer le changement de sélection"""
        selected_rows = self.suppliers_table.selectionModel().selectedRows()

        if selected_rows:
            row = selected_rows[0].row()
            if 0 <= row < len(self.current_suppliers):
                self.selected_supplier = self.current_suppliers[row]
                self._update_details_panel()
                self.edit_button.setEnabled(True)
                self.delete_button.setEnabled(True)
                self.supplier_selected.emit(self.selected_supplier)
        else:
            self.selected_supplier = None
            self._clear_details_panel()
            self.edit_button.setEnabled(False)
            self.delete_button.setEnabled(False)

    def _update_details_panel(self):
        """Mettre à jour le panneau de détails"""
        if not self.selected_supplier:
            return

        supplier = self.selected_supplier

        # Onglet informations générales
        details_html = f"""
        <h3>{supplier.get('full_name', 'N/A')}</h3>
        <p><b>Personne de contact:</b> {supplier.get('contact_person', 'N/A')}</p>
        <p><b>Email:</b> {supplier.get('email', 'N/A')}</p>
        <p><b>Téléphone:</b> {supplier.get('phone', 'N/A')}</p>
        <p><b>Mobile:</b> {supplier.get('mobile', 'N/A')}</p>
        <hr>
        <p><b>Adresse:</b><br>{supplier.get('full_address', 'Non renseignée')}</p>
        <hr>
        <p><b>Catégorie:</b> {supplier.get('category', 'N/A')}</p>
        <p><b>Note:</b> {supplier.get('rating_stars', 'N/A')} ({supplier.get('rating', 0)}/5)</p>
        <p><b>Statut:</b> <span style="color: {supplier.get('status_color', '#000')}">{supplier.get('status_text', 'N/A')}</span></p>
        <hr>
        <p><b>Conditions de paiement:</b> {supplier.get('payment_info', 'N/A')}</p>
        <p><b>Délai de livraison:</b> {supplier.get('delivery_time', 0)} jours</p>
        <p><b>Commande minimum:</b> {supplier.get('formatted_minimum_order', 'N/A')}</p>
        """

        self.general_content.setText(details_html)

        # Onglet articles associés
        self._update_articles_tab()

    def _update_articles_tab(self):
        """Mettre à jour l'onglet des articles"""
        if not self.selected_supplier:
            return

        try:
            # Récupérer les articles du fournisseur
            supplier_id = self.selected_supplier['id']
            articles = self.article_controller.get_all({'supplier_id': supplier_id})

            if articles:
                articles_html = f"<h4>{len(articles)} article(s) associé(s):</h4><ul>"
                for article in articles:
                    stock_status = "✅" if article.get('quantity_in_stock', 0) > article.get('minimum_stock', 0) else "⚠️"
                    articles_html += f"""
                    <li><b>{article.get('name', 'N/A')}</b> ({article.get('reference', 'N/A')})<br>
                    Stock: {article.get('quantity_in_stock', 0)} {article.get('unit', 'pièce')} {stock_status}<br>
                    Prix: {article.get('unit_price', 0):.2f} €</li>
                    """
                articles_html += "</ul>"
            else:
                articles_html = "<p>Aucun article associé à ce fournisseur.</p>"

            self.articles_content.setText(articles_html)

        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des articles: {e}")
            self.articles_content.setText("Erreur lors du chargement des articles")

    def _clear_details_panel(self):
        """Vider le panneau de détails"""
        self.general_content.setText("Sélectionnez un fournisseur pour voir ses détails")
        self.articles_content.setText("Aucun article associé")

    def _add_supplier(self):
        """Ajouter un nouveau fournisseur"""
        dialog = SupplierDialog(self, "Nouveau Fournisseur")
        if dialog.exec_() == QDialog.Accepted:
            supplier_data = dialog.get_supplier_data()
            result = self.controller.create_supplier(supplier_data)

            if result.success:
                self._show_success("Succès", result.message)
                self._load_suppliers()
            else:
                self._show_error("Erreur", result.message)

    def _edit_supplier(self):
        """Modifier le fournisseur sélectionné"""
        if not self.selected_supplier:
            return

        dialog = SupplierDialog(self, "Modifier Fournisseur", self.selected_supplier)
        if dialog.exec_() == QDialog.Accepted:
            supplier_data = dialog.get_supplier_data()
            result = self.controller.update_supplier(self.selected_supplier['id'], supplier_data)

            if result.success:
                self._show_success("Succès", result.message)
                self._load_suppliers()
            else:
                self._show_error("Erreur", result.message)

    def _delete_supplier(self):
        """Supprimer le fournisseur sélectionné"""
        if not self.selected_supplier:
            return

        reply = QMessageBox.question(
            self,
            "Confirmer la suppression",
            f"Êtes-vous sûr de vouloir supprimer le fournisseur '{self.selected_supplier['name']}'?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            success, message = self.controller.delete(self.selected_supplier['id'])

            if success:
                self._show_success("Succès", message)
                self._load_suppliers()
            else:
                self._show_error("Erreur", message)

    def _show_success(self, title: str, message: str):
        """Afficher un message de succès"""
        if FLUENT_AVAILABLE:
            InfoBar.success(title, message, parent=self)
        else:
            QMessageBox.information(self, title, message)

    def _show_error(self, title: str, message: str):
        """Afficher un message d'erreur"""
        if FLUENT_AVAILABLE:
            InfoBar.error(title, message, parent=self)
        else:
            QMessageBox.critical(self, title, message)


class SupplierDialog(QDialog):
    """Dialog pour créer/modifier un fournisseur"""

    def __init__(self, parent, title: str, supplier_data: dict = None):
        super().__init__(parent)
        self.supplier_data = supplier_data or {}

        self.setWindowTitle(title)
        self.setModal(True)
        self.setFixedSize(600, 700)

        self._init_ui()
        self._populate_fields()

    def _init_ui(self):
        """Initialiser l'interface du dialog"""
        layout = QVBoxLayout()

        # Onglets pour organiser les champs
        tabs = QTabWidget()

        # Onglet Informations générales
        general_tab = QWidget()
        self._create_general_tab(general_tab)
        tabs.addTab(general_tab, "Informations générales")

        # Onglet Contact
        contact_tab = QWidget()
        self._create_contact_tab(contact_tab)
        tabs.addTab(contact_tab, "Contact")

        # Onglet Commercial
        commercial_tab = QWidget()
        self._create_commercial_tab(commercial_tab)
        tabs.addTab(commercial_tab, "Commercial")

        layout.addWidget(tabs)

        # Boutons
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()

        self.save_button = QPushButton("Enregistrer")
        self.cancel_button = QPushButton("Annuler")

        self.save_button.clicked.connect(self.accept)
        self.cancel_button.clicked.connect(self.reject)

        buttons_layout.addWidget(self.save_button)
        buttons_layout.addWidget(self.cancel_button)

        layout.addLayout(buttons_layout)
        self.setLayout(layout)

    def _create_general_tab(self, tab):
        """Créer l'onglet informations générales"""
        layout = QFormLayout()

        # Informations de base
        self.name_input = QLineEdit()
        self.company_name_input = QLineEdit()
        self.category_combo = QComboBox()
        self.category_combo.addItems(get_supplier_categories())

        layout.addRow("Nom*:", self.name_input)
        layout.addRow("Nom de l'entreprise:", self.company_name_input)
        layout.addRow("Catégorie:", self.category_combo)

        # Adresse
        layout.addRow(QLabel(""))  # Séparateur
        address_label = QLabel("Adresse")
        address_label.setStyleSheet("font-weight: bold;")
        layout.addRow(address_label)

        self.address_input = QTextEdit()
        self.address_input.setMaximumHeight(60)
        self.city_input = QLineEdit()
        self.postal_code_input = QLineEdit()
        self.country_input = QLineEdit()
        self.country_input.setText("France")

        layout.addRow("Adresse:", self.address_input)
        layout.addRow("Ville:", self.city_input)
        layout.addRow("Code postal:", self.postal_code_input)
        layout.addRow("Pays:", self.country_input)

        # Statut et note
        layout.addRow(QLabel(""))  # Séparateur
        self.is_active_checkbox = QCheckBox("Fournisseur actif")
        self.is_active_checkbox.setChecked(True)

        self.rating_spinbox = QSpinBox()
        self.rating_spinbox.setRange(0, 5)
        self.rating_spinbox.setSuffix(" étoiles")

        layout.addRow("", self.is_active_checkbox)
        layout.addRow("Note:", self.rating_spinbox)

        # Notes
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(80)
        layout.addRow("Notes:", self.notes_input)

        tab.setLayout(layout)

    def _create_contact_tab(self, tab):
        """Créer l'onglet contact"""
        layout = QFormLayout()

        # Personne de contact
        self.contact_person_input = QLineEdit()
        layout.addRow("Personne de contact:", self.contact_person_input)

        # Coordonnées
        self.email_input = QLineEdit()
        self.phone_input = QLineEdit()
        self.mobile_input = QLineEdit()
        self.fax_input = QLineEdit()
        self.website_input = QLineEdit()

        layout.addRow("Email:", self.email_input)
        layout.addRow("Téléphone:", self.phone_input)
        layout.addRow("Mobile:", self.mobile_input)
        layout.addRow("Fax:", self.fax_input)
        layout.addRow("Site web:", self.website_input)

        # Informations légales
        layout.addRow(QLabel(""))  # Séparateur
        legal_label = QLabel("Informations légales")
        legal_label.setStyleSheet("font-weight: bold;")
        layout.addRow(legal_label)

        self.tax_number_input = QLineEdit()
        layout.addRow("Numéro de TVA:", self.tax_number_input)

        # Coordonnées bancaires
        self.bank_details_input = QTextEdit()
        self.bank_details_input.setMaximumHeight(80)
        layout.addRow("Coordonnées bancaires:", self.bank_details_input)

        tab.setLayout(layout)

    def _create_commercial_tab(self, tab):
        """Créer l'onglet commercial"""
        layout = QFormLayout()

        # Conditions de paiement
        self.payment_terms_spinbox = QSpinBox()
        self.payment_terms_spinbox.setRange(0, 365)
        self.payment_terms_spinbox.setValue(30)
        self.payment_terms_spinbox.setSuffix(" jours")

        self.payment_method_combo = QComboBox()
        self.payment_method_combo.addItems(get_payment_methods())

        layout.addRow("Délai de paiement:", self.payment_terms_spinbox)
        layout.addRow("Mode de paiement:", self.payment_method_combo)

        # Conditions commerciales
        self.discount_rate_spinbox = QDoubleSpinBox()
        self.discount_rate_spinbox.setRange(0, 100)
        self.discount_rate_spinbox.setDecimals(2)
        self.discount_rate_spinbox.setSuffix(" %")

        self.credit_limit_spinbox = QDoubleSpinBox()
        self.credit_limit_spinbox.setRange(0, 999999.99)
        self.credit_limit_spinbox.setDecimals(2)
        self.credit_limit_spinbox.setSuffix(" €")

        self.minimum_order_spinbox = QDoubleSpinBox()
        self.minimum_order_spinbox.setRange(0, 999999.99)
        self.minimum_order_spinbox.setDecimals(2)
        self.minimum_order_spinbox.setSuffix(" €")

        layout.addRow("Taux de remise:", self.discount_rate_spinbox)
        layout.addRow("Limite de crédit:", self.credit_limit_spinbox)
        layout.addRow("Commande minimum:", self.minimum_order_spinbox)

        # Livraison
        self.delivery_time_spinbox = QSpinBox()
        self.delivery_time_spinbox.setRange(0, 365)
        self.delivery_time_spinbox.setValue(7)
        self.delivery_time_spinbox.setSuffix(" jours")

        self.currency_combo = QComboBox()
        self.currency_combo.addItems(["EUR", "USD", "GBP", "CHF"])

        layout.addRow("Délai de livraison:", self.delivery_time_spinbox)
        layout.addRow("Devise:", self.currency_combo)

        tab.setLayout(layout)

    def _populate_fields(self):
        """Remplir les champs avec les données existantes"""
        if not self.supplier_data:
            return

        # Onglet général
        self.name_input.setText(self.supplier_data.get('name', ''))
        self.company_name_input.setText(self.supplier_data.get('company_name', ''))

        category = self.supplier_data.get('category', 'Général')
        index = self.category_combo.findText(category)
        if index >= 0:
            self.category_combo.setCurrentIndex(index)

        self.address_input.setPlainText(self.supplier_data.get('address', ''))
        self.city_input.setText(self.supplier_data.get('city', ''))
        self.postal_code_input.setText(self.supplier_data.get('postal_code', ''))
        self.country_input.setText(self.supplier_data.get('country', 'France'))

        self.is_active_checkbox.setChecked(self.supplier_data.get('is_active', True))
        self.rating_spinbox.setValue(self.supplier_data.get('rating', 0))
        self.notes_input.setPlainText(self.supplier_data.get('notes', ''))

        # Onglet contact
        self.contact_person_input.setText(self.supplier_data.get('contact_person', ''))
        self.email_input.setText(self.supplier_data.get('email', ''))
        self.phone_input.setText(self.supplier_data.get('phone', ''))
        self.mobile_input.setText(self.supplier_data.get('mobile', ''))
        self.fax_input.setText(self.supplier_data.get('fax', ''))
        self.website_input.setText(self.supplier_data.get('website', ''))
        self.tax_number_input.setText(self.supplier_data.get('tax_number', ''))
        self.bank_details_input.setPlainText(self.supplier_data.get('bank_details', ''))

        # Onglet commercial
        self.payment_terms_spinbox.setValue(self.supplier_data.get('payment_terms', 30))

        payment_method = self.supplier_data.get('payment_method', 'Virement')
        index = self.payment_method_combo.findText(payment_method)
        if index >= 0:
            self.payment_method_combo.setCurrentIndex(index)

        self.discount_rate_spinbox.setValue(float(self.supplier_data.get('discount_rate', 0)))
        self.credit_limit_spinbox.setValue(float(self.supplier_data.get('credit_limit', 0)))
        self.minimum_order_spinbox.setValue(float(self.supplier_data.get('minimum_order', 0)))
        self.delivery_time_spinbox.setValue(self.supplier_data.get('delivery_time', 7))

        currency = self.supplier_data.get('currency', 'EUR')
        index = self.currency_combo.findText(currency)
        if index >= 0:
            self.currency_combo.setCurrentIndex(index)

    def get_supplier_data(self) -> dict:
        """Récupérer les données du formulaire"""
        return {
            # Général
            'name': self.name_input.text().strip(),
            'company_name': self.company_name_input.text().strip(),
            'category': self.category_combo.currentText(),
            'address': self.address_input.toPlainText().strip(),
            'city': self.city_input.text().strip(),
            'postal_code': self.postal_code_input.text().strip(),
            'country': self.country_input.text().strip(),
            'is_active': self.is_active_checkbox.isChecked(),
            'rating': self.rating_spinbox.value(),
            'notes': self.notes_input.toPlainText().strip(),

            # Contact
            'contact_person': self.contact_person_input.text().strip(),
            'email': self.email_input.text().strip(),
            'phone': self.phone_input.text().strip(),
            'mobile': self.mobile_input.text().strip(),
            'fax': self.fax_input.text().strip(),
            'website': self.website_input.text().strip(),
            'tax_number': self.tax_number_input.text().strip(),
            'bank_details': self.bank_details_input.toPlainText().strip(),

            # Commercial
            'payment_terms': self.payment_terms_spinbox.value(),
            'payment_method': self.payment_method_combo.currentText(),
            'discount_rate': self.discount_rate_spinbox.value(),
            'credit_limit': self.credit_limit_spinbox.value(),
            'minimum_order': self.minimum_order_spinbox.value(),
            'delivery_time': self.delivery_time_spinbox.value(),
            'currency': self.currency_combo.currentText()
        }
