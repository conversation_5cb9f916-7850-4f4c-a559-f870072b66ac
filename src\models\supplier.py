"""
Modèle Fournisseur pour la gestion des fournisseurs
"""

from datetime import datetime
from typing import Dict, Any, Optional, List

from .base import BaseModel, TimestampMixin, validate_email, validate_phone, validate_non_empty_string


class Supplier(BaseModel, TimestampMixin):
    """Modèle pour les fournisseurs"""
    
    def _set_defaults(self):
        """Définir les valeurs par défaut"""
        self._data.update({
            'id': None,
            'name': '',
            'company_name': '',
            'contact_person': '',
            'email': '',
            'phone': '',
            'mobile': '',
            'fax': '',
            'website': '',
            'address': '',
            'city': '',
            'postal_code': '',
            'country': 'France',
            'tax_number': '',
            'payment_terms': 30,  # Jours
            'payment_method': 'Virement',
            'discount_rate': 0.0,
            'credit_limit': 0.0,
            'currency': 'EUR',
            'is_active': True,
            'rating': 0,  # Note de 0 à 5
            'notes': '',
            'delivery_time': 7,  # Jours
            'minimum_order': 0.0,
            'bank_details': '',
            'category': 'Général'
        })
        self._set_timestamp_defaults()
    
    def get_validation_rules(self) -> Dict[str, Dict]:
        """Règles de validation pour les fournisseurs"""
        return {
            'name': {
                'required': True,
                'type': str,
                'min_length': 2,
                'max_length': 200,
                'validator': validate_non_empty_string,
                'validator_message': "Le nom du fournisseur ne peut pas être vide"
            },
            'email': {
                'type': str,
                'validator': lambda x: not x or validate_email(x),
                'validator_message': "Format d'email invalide"
            },
            'phone': {
                'type': str,
                'validator': lambda x: not x or validate_phone(x),
                'validator_message': "Format de téléphone invalide"
            },
            'mobile': {
                'type': str,
                'validator': lambda x: not x or validate_phone(x),
                'validator_message': "Format de mobile invalide"
            },
            'payment_terms': {
                'type': int,
                'min_value': 0,
                'max_value': 365,
                'validator_message': "Les conditions de paiement doivent être entre 0 et 365 jours"
            },
            'discount_rate': {
                'type': (float, int),
                'min_value': 0,
                'max_value': 100,
                'validator_message': "Le taux de remise doit être entre 0 et 100%"
            },
            'credit_limit': {
                'type': (float, int),
                'min_value': 0,
                'validator_message': "La limite de crédit doit être positive"
            },
            'rating': {
                'type': int,
                'min_value': 0,
                'max_value': 5,
                'validator_message': "La note doit être entre 0 et 5"
            },
            'delivery_time': {
                'type': int,
                'min_value': 0,
                'max_value': 365,
                'validator_message': "Le délai de livraison doit être entre 0 et 365 jours"
            },
            'minimum_order': {
                'type': (float, int),
                'min_value': 0,
                'validator_message': "La commande minimum doit être positive"
            }
        }
    
    # Propriétés calculées
    def get_full_name(self) -> str:
        """Obtenir le nom complet (nom + entreprise)"""
        name = self._data['name']
        company = self._data['company_name']
        
        if company and company != name:
            return f"{name} ({company})"
        return name
    
    def get_primary_contact(self) -> str:
        """Obtenir le contact principal"""
        contacts = []
        if self._data['email']:
            contacts.append(self._data['email'])
        if self._data['phone']:
            contacts.append(self._data['phone'])
        if self._data['mobile']:
            contacts.append(self._data['mobile'])
        
        return " | ".join(contacts) if contacts else "Aucun contact"
    
    def get_full_address(self) -> str:
        """Obtenir l'adresse complète"""
        parts = []
        if self._data['address']:
            parts.append(self._data['address'])
        if self._data['postal_code'] and self._data['city']:
            parts.append(f"{self._data['postal_code']} {self._data['city']}")
        elif self._data['city']:
            parts.append(self._data['city'])
        if self._data['country'] and self._data['country'] != 'France':
            parts.append(self._data['country'])
        
        return "\n".join(parts) if parts else "Adresse non renseignée"
    
    def get_payment_info(self) -> str:
        """Obtenir les informations de paiement"""
        terms = self._data['payment_terms']
        method = self._data['payment_method']
        discount = self._data['discount_rate']
        
        info = f"{method} - {terms} jours"
        if discount > 0:
            info += f" (Remise: {discount}%)"
        
        return info
    
    def get_rating_stars(self) -> str:
        """Obtenir la note sous forme d'étoiles"""
        rating = self._data['rating']
        return "★" * rating + "☆" * (5 - rating)
    
    def get_status_color(self) -> str:
        """Obtenir la couleur du statut"""
        if not self._data['is_active']:
            return "#6c757d"  # Gris
        
        rating = self._data['rating']
        if rating >= 4:
            return "#28a745"  # Vert
        elif rating >= 3:
            return "#ffc107"  # Jaune
        elif rating >= 2:
            return "#fd7e14"  # Orange
        else:
            return "#dc3545"  # Rouge
    
    # Méthodes de gestion
    def activate(self):
        """Activer le fournisseur"""
        self._data['is_active'] = True
        self.touch()
        self.logger.info(f"Fournisseur activé: {self._data['name']}")
    
    def deactivate(self):
        """Désactiver le fournisseur"""
        self._data['is_active'] = False
        self.touch()
        self.logger.info(f"Fournisseur désactivé: {self._data['name']}")
    
    def set_rating(self, rating: int):
        """Définir la note du fournisseur"""
        if 0 <= rating <= 5:
            self._data['rating'] = rating
            self.touch()
            self.logger.info(f"Note mise à jour pour {self._data['name']}: {rating}/5")
    
    def update_contact_info(self, **contact_data):
        """Mettre à jour les informations de contact"""
        contact_fields = ['email', 'phone', 'mobile', 'fax', 'website', 'contact_person']
        updated_fields = []
        
        for field, value in contact_data.items():
            if field in contact_fields:
                self._data[field] = value
                updated_fields.append(field)
        
        if updated_fields:
            self.touch()
            self.logger.info(f"Contact mis à jour pour {self._data['name']}: {', '.join(updated_fields)}")
    
    def update_address(self, address: str = None, city: str = None, 
                      postal_code: str = None, country: str = None):
        """Mettre à jour l'adresse"""
        if address is not None:
            self._data['address'] = address
        if city is not None:
            self._data['city'] = city
        if postal_code is not None:
            self._data['postal_code'] = postal_code
        if country is not None:
            self._data['country'] = country
        
        self.touch()
        self.logger.info(f"Adresse mise à jour pour {self._data['name']}")
    
    # Méthodes de recherche et filtrage
    def matches_search(self, search_term: str) -> bool:
        """Vérifier si le fournisseur correspond à un terme de recherche"""
        search_term = search_term.lower()
        searchable_fields = [
            self._data['name'],
            self._data['company_name'],
            self._data['contact_person'],
            self._data['email'],
            self._data['phone'],
            self._data['mobile'],
            self._data['city'],
            self._data['category'],
            self._data['notes']
        ]
        
        return any(search_term in str(field).lower() for field in searchable_fields if field)
    
    def to_display_dict(self) -> Dict[str, Any]:
        """Convertir en dictionnaire pour l'affichage"""
        data = self.to_dict()
        data.update({
            'full_name': self.get_full_name(),
            'primary_contact': self.get_primary_contact(),
            'full_address': self.get_full_address(),
            'payment_info': self.get_payment_info(),
            'rating_stars': self.get_rating_stars(),
            'status_color': self.get_status_color(),
            'status_text': "Actif" if self._data['is_active'] else "Inactif",
            'formatted_credit_limit': f"{self._data['credit_limit']:.2f} {self._data['currency']}",
            'formatted_minimum_order': f"{self._data['minimum_order']:.2f} {self._data['currency']}",
            'formatted_discount': f"{self._data['discount_rate']:.1f}%"
        })
        return data
    
    def to_contact_card(self) -> Dict[str, str]:
        """Convertir en carte de contact"""
        return {
            'name': self.get_full_name(),
            'contact_person': self._data['contact_person'],
            'email': self._data['email'],
            'phone': self._data['phone'],
            'mobile': self._data['mobile'],
            'address': self.get_full_address(),
            'website': self._data['website']
        }


# Fonctions utilitaires pour les fournisseurs
def create_supplier_from_dict(data: Dict[str, Any]) -> Supplier:
    """Créer un fournisseur à partir d'un dictionnaire"""
    supplier = Supplier(**data)
    if not supplier.validate():
        from .base import ValidationError
        raise ValidationError(supplier.get_errors())
    return supplier


def get_payment_methods() -> List[str]:
    """Obtenir la liste des méthodes de paiement disponibles"""
    return [
        'Virement',
        'Chèque',
        'Carte bancaire',
        'Espèces',
        'Lettre de change',
        'Prélèvement',
        'PayPal',
        'Autre'
    ]


def get_supplier_categories() -> List[str]:
    """Obtenir la liste des catégories de fournisseurs"""
    return [
        'Général',
        'Matières premières',
        'Équipements',
        'Services',
        'Maintenance',
        'Transport',
        'Informatique',
        'Bureautique',
        'Autre'
    ]
