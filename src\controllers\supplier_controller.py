"""
Contrôleur pour la gestion des fournisseurs
"""

from typing import Dict, Any, List, Optional, Tuple

from .base import BaseController, CRUDResult
from models.supplier import Supplier, create_supplier_from_dict
from models.base import ValidationError


class SupplierController(BaseController):
    """Contrôleur pour la gestion des fournisseurs"""
    
    def get_table_name(self) -> str:
        """Nom de la table des fournisseurs"""
        return "suppliers"
    
    def _get_searchable_fields(self) -> List[str]:
        """Champs recherchables pour les fournisseurs"""
        return ['name', 'company_name', 'contact_person', 'email', 'phone', 'city']
    
    def _get_default_order(self) -> str:
        """Ordre par défaut pour les fournisseurs"""
        return "name ASC"
    
    def _validate_data(self, data: Dict[str, Any], is_update: bool = False) -> Tuple[bool, str]:
        """Valider les données de fournisseur"""
        try:
            # <PERSON><PERSON>er un objet Supplier pour validation
            supplier = Supplier(**data)
            
            if not supplier.validate():
                errors = supplier.get_error_messages()
                return False, "; ".join(errors)
            
            # Vérifications supplémentaires
            if not is_update or 'email' in data:
                # Vérifier l'unicité de l'email s'il est fourni
                email = data.get('email')
                if email and self._is_email_duplicate(email, data.get('id')):
                    return False, "Cette adresse email existe déjà"
            
            return True, "Validation réussie"
            
        except Exception as e:
            return False, f"Erreur de validation: {e}"
    
    def _is_email_duplicate(self, email: str, exclude_id: int = None) -> bool:
        """Vérifier si un email existe déjà"""
        try:
            query = "SELECT id FROM suppliers WHERE email = ? AND email != ''"
            params = [email]
            
            if exclude_id:
                query += " AND id != ?"
                params.append(exclude_id)
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                return cursor.fetchone() is not None
                
        except Exception as e:
            self.logger.error(f"Erreur lors de la vérification d'email: {e}")
            return False
    
    def _check_delete_constraints(self, record_id: int) -> Tuple[bool, str]:
        """Vérifier les contraintes avant suppression d'un fournisseur"""
        try:
            # Vérifier s'il y a des articles associés
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) as count FROM articles WHERE supplier_id = ?", (record_id,))
                article_count = cursor.fetchone()['count']

                if article_count > 0:
                    return False, f"Impossible de supprimer: {article_count} article(s) associé(s)"

                # Vérifier s'il y a des commandes
                cursor.execute("SELECT COUNT(*) as count FROM orders WHERE supplier_id = ?", (record_id,))
                order_count = cursor.fetchone()['count']

                if order_count > 0:
                    return False, f"Impossible de supprimer: {order_count} commande(s) associée(s)"
            
            return True, "Suppression autorisée"
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la vérification des contraintes: {e}")
            return False, "Erreur lors de la vérification"
    
    # Méthodes spécifiques aux fournisseurs
    def create_supplier(self, supplier_data: Dict[str, Any]) -> CRUDResult:
        """Créer un nouveau fournisseur"""
        try:
            # Valider avec le modèle Supplier
            supplier = create_supplier_from_dict(supplier_data)
            
            # Créer en base
            success, result = self.create(supplier.to_dict())
            
            if success:
                # Récupérer le fournisseur créé
                created_supplier = self.get_by_id(result)
                return CRUDResult(True, created_supplier, f"Fournisseur '{supplier_data.get('name')}' créé avec succès")
            else:
                return CRUDResult(False, None, f"Erreur lors de la création: {result}")
                
        except ValidationError as e:
            return CRUDResult(False, None, "Données invalides", e.errors)
        except Exception as e:
            self.logger.error(f"Erreur lors de la création de fournisseur: {e}")
            return CRUDResult(False, None, f"Erreur inattendue: {e}")
    
    def update_supplier(self, supplier_id: int, supplier_data: Dict[str, Any]) -> CRUDResult:
        """Mettre à jour un fournisseur"""
        try:
            # Récupérer le fournisseur existant
            existing_supplier = self.get_by_id(supplier_id)
            if not existing_supplier:
                return CRUDResult(False, None, "Fournisseur non trouvé")
            
            # Fusionner les données
            updated_data = existing_supplier.copy()
            updated_data.update(supplier_data)
            
            # Valider avec le modèle Supplier
            supplier = create_supplier_from_dict(updated_data)
            
            # Mettre à jour en base
            success, message = self.update(supplier_id, supplier_data)
            
            if success:
                # Récupérer le fournisseur mis à jour
                updated_supplier = self.get_by_id(supplier_id)
                return CRUDResult(True, updated_supplier, f"Fournisseur '{updated_supplier.get('name')}' mis à jour")
            else:
                return CRUDResult(False, None, f"Erreur lors de la mise à jour: {message}")
                
        except ValidationError as e:
            return CRUDResult(False, None, "Données invalides", e.errors)
        except Exception as e:
            self.logger.error(f"Erreur lors de la mise à jour de fournisseur: {e}")
            return CRUDResult(False, None, f"Erreur inattendue: {e}")
    
    def get_supplier_with_details(self, supplier_id: int) -> Optional[Dict[str, Any]]:
        """Récupérer un fournisseur avec ses détails calculés"""
        try:
            supplier_data = self.get_by_id(supplier_id)
            if not supplier_data:
                return None
            
            # Créer un objet Supplier pour les calculs
            supplier = Supplier(**supplier_data)
            
            # Ajouter les statistiques
            details = supplier.to_display_dict()
            details.update(self._get_supplier_statistics(supplier_id))
            
            return details
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des détails: {e}")
            return None
    
    def get_suppliers_with_details(self, filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Récupérer tous les fournisseurs avec leurs détails calculés"""
        try:
            suppliers_data = self.get_all(filters)
            suppliers_with_details = []
            
            for supplier_data in suppliers_data:
                supplier = Supplier(**supplier_data)
                details = supplier.to_display_dict()
                details.update(self._get_supplier_statistics(supplier_data['id']))
                suppliers_with_details.append(details)
            
            return suppliers_with_details
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des fournisseurs: {e}")
            return []
    
    def _get_supplier_statistics(self, supplier_id: int) -> Dict[str, Any]:
        """Obtenir les statistiques d'un fournisseur"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()

                # Nombre d'articles
                cursor.execute("SELECT COUNT(*) as count FROM articles WHERE supplier_id = ?", (supplier_id,))
                article_count = cursor.fetchone()['count']

                # Nombre de commandes
                cursor.execute("SELECT COUNT(*) as count FROM orders WHERE supplier_id = ?", (supplier_id,))
                order_count = cursor.fetchone()['count']

                # Valeur totale des articles
                cursor.execute("""
                    SELECT SUM(quantity_in_stock * unit_price) as value
                    FROM articles
                    WHERE supplier_id = ? AND is_active = 1
                """, (supplier_id,))
                total_value = cursor.fetchone()['value'] or 0

                # Dernière commande
                cursor.execute("""
                    SELECT MAX(created_at) as last_order
                    FROM orders
                    WHERE supplier_id = ?
                """, (supplier_id,))
                last_order = cursor.fetchone()['last_order']

                return {
                    'article_count': article_count,
                    'order_count': order_count,
                    'total_value': float(total_value),
                    'last_order_date': last_order,
                    'formatted_total_value': f"{total_value:.2f} €"
                }
                
        except Exception as e:
            self.logger.error(f"Erreur lors du calcul des statistiques fournisseur: {e}")
            return {
                'article_count': 0,
                'order_count': 0,
                'total_value': 0.0,
                'last_order_date': None,
                'formatted_total_value': "0.00 €"
            }
    
    def activate_supplier(self, supplier_id: int) -> CRUDResult:
        """Activer un fournisseur"""
        try:
            success, message = self.update(supplier_id, {'is_active': True})
            
            if success:
                supplier = self.get_by_id(supplier_id)
                return CRUDResult(True, supplier, f"Fournisseur '{supplier.get('name')}' activé")
            else:
                return CRUDResult(False, None, f"Erreur lors de l'activation: {message}")
                
        except Exception as e:
            self.logger.error(f"Erreur lors de l'activation du fournisseur: {e}")
            return CRUDResult(False, None, f"Erreur inattendue: {e}")
    
    def deactivate_supplier(self, supplier_id: int) -> CRUDResult:
        """Désactiver un fournisseur"""
        try:
            success, message = self.update(supplier_id, {'is_active': False})
            
            if success:
                supplier = self.get_by_id(supplier_id)
                return CRUDResult(True, supplier, f"Fournisseur '{supplier.get('name')}' désactivé")
            else:
                return CRUDResult(False, None, f"Erreur lors de la désactivation: {message}")
                
        except Exception as e:
            self.logger.error(f"Erreur lors de la désactivation du fournisseur: {e}")
            return CRUDResult(False, None, f"Erreur inattendue: {e}")
    
    def update_rating(self, supplier_id: int, rating: int) -> CRUDResult:
        """Mettre à jour la note d'un fournisseur"""
        try:
            if not 0 <= rating <= 5:
                return CRUDResult(False, None, "La note doit être entre 0 et 5")
            
            success, message = self.update(supplier_id, {'rating': rating})
            
            if success:
                supplier = self.get_by_id(supplier_id)
                return CRUDResult(True, supplier, f"Note mise à jour: {rating}/5")
            else:
                return CRUDResult(False, None, f"Erreur lors de la mise à jour: {message}")
                
        except Exception as e:
            self.logger.error(f"Erreur lors de la mise à jour de la note: {e}")
            return CRUDResult(False, None, f"Erreur inattendue: {e}")
    
    def search_suppliers(self, search_term: str, filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Rechercher des fournisseurs avec filtres avancés"""
        try:
            # Recherche de base
            suppliers = self.search(search_term)
            
            # Appliquer les filtres supplémentaires
            if filters:
                filtered_suppliers = []
                for supplier_data in suppliers:
                    supplier = Supplier(**supplier_data)
                    
                    # Filtrer par statut actif
                    if filters.get('is_active') is not None and supplier_data.get('is_active') != filters['is_active']:
                        continue
                    
                    # Filtrer par note
                    if filters.get('min_rating') and supplier_data.get('rating', 0) < filters['min_rating']:
                        continue
                    
                    # Filtrer par catégorie
                    if filters.get('category') and supplier_data.get('category') != filters['category']:
                        continue
                    
                    # Filtrer par pays
                    if filters.get('country') and supplier_data.get('country') != filters['country']:
                        continue
                    
                    details = supplier.to_display_dict()
                    details.update(self._get_supplier_statistics(supplier_data['id']))
                    filtered_suppliers.append(details)
                
                return filtered_suppliers
            else:
                # Retourner avec détails calculés
                suppliers_with_details = []
                for supplier_data in suppliers:
                    supplier = Supplier(**supplier_data)
                    details = supplier.to_display_dict()
                    details.update(self._get_supplier_statistics(supplier_data['id']))
                    suppliers_with_details.append(details)
                
                return suppliers_with_details
                
        except Exception as e:
            self.logger.error(f"Erreur lors de la recherche de fournisseurs: {e}")
            return []
    
    def get_active_suppliers(self) -> List[Dict[str, Any]]:
        """Récupérer uniquement les fournisseurs actifs"""
        return self.get_suppliers_with_details({'is_active': True})
    
    def get_top_suppliers(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Récupérer les meilleurs fournisseurs (par note et nombre d'articles)"""
        # Méthode temporairement simplifiée pour éviter les erreurs d'indentation
        return self.get_active_suppliers()[:limit]

    def get_supplier_statistics(self) -> Dict[str, Any]:
        """Obtenir des statistiques sur les fournisseurs"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()

                # Statistiques générales
                query = """
                    SELECT
                        COUNT(*) as total_suppliers,
                        COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_suppliers,
                        COUNT(CASE WHEN is_active = 0 THEN 1 END) as inactive_suppliers,
                        AVG(rating) as average_rating
                    FROM suppliers
                """

                cursor.execute(query)
                result = cursor.fetchone()

                return dict(result) if result else {}

        except Exception as e:
            self.logger.error(f"Erreur lors du calcul des statistiques: {e}")
            return {}
