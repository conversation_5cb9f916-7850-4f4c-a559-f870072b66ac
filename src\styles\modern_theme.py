"""
Thème moderne pour l'application GSlim
"""

from PyQt5.QtCore import Qt
from PyQt5.QtGui import QColor, QPalette, QFont

class ModernTheme:
    """Classe pour gérer les thèmes modernes"""
    
    # Couleurs principales
    PRIMARY_COLOR = "#2563eb"  # Bleu moderne
    SECONDARY_COLOR = "#64748b"  # Gris bleu
    ACCENT_COLOR = "#06b6d4"  # Cyan
    SUCCESS_COLOR = "#10b981"  # Vert
    WARNING_COLOR = "#f59e0b"  # Orange
    ERROR_COLOR = "#ef4444"  # Rouge
    
    # Couleurs de fond
    BACKGROUND_PRIMARY = "#ffffff"  # Blanc
    BACKGROUND_SECONDARY = "#f8fafc"  # Gris très clair
    BACKGROUND_TERTIARY = "#f1f5f9"  # Gris clair
    
    # Couleurs de texte
    TEXT_PRIMARY = "#1e293b"  # Gris foncé
    TEXT_SECONDARY = "#64748b"  # Gris moyen
    TEXT_MUTED = "#94a3b8"  # Gris clair
    
    # Couleurs de bordure
    BORDER_COLOR = "#e2e8f0"  # Gris très clair
    BORDER_HOVER = "#cbd5e1"  # Gris clair
    
    @staticmethod
    def get_modern_stylesheet():
        """Retourner le stylesheet moderne pour l'application"""
        return f"""
        /* Styles généraux */
        QMainWindow {{
            background-color: {ModernTheme.BACKGROUND_SECONDARY};
            color: {ModernTheme.TEXT_PRIMARY};
            font-family: 'Segoe UI', 'Arial', sans-serif;
        }}
        
        /* Navigation */
        QWidget[class="navigation"] {{
            background-color: {ModernTheme.BACKGROUND_PRIMARY};
            border-right: 1px solid {ModernTheme.BORDER_COLOR};
        }}
        
        /* Cartes modernes */
        QWidget[class="card"] {{
            background-color: {ModernTheme.BACKGROUND_PRIMARY};
            border: 1px solid {ModernTheme.BORDER_COLOR};
            border-radius: 12px;
            padding: 20px;
            margin: 8px;
        }}
        
        QWidget[class="card"]:hover {{
            border-color: {ModernTheme.BORDER_HOVER};
        }}
        
        /* Cartes de statistiques */
        QWidget[class="stat-card"] {{
            background-color: {ModernTheme.BACKGROUND_PRIMARY};
            border: 1px solid {ModernTheme.BORDER_COLOR};
            border-radius: 16px;
            padding: 24px;
            margin: 12px;
            min-height: 120px;
        }}
        
        QWidget[class="stat-card-primary"] {{
            background: linear-gradient(135deg, {ModernTheme.PRIMARY_COLOR} 0%, #3b82f6 100%);
            color: white;
            border: none;
        }}
        
        QWidget[class="stat-card-success"] {{
            background: linear-gradient(135deg, {ModernTheme.SUCCESS_COLOR} 0%, #059669 100%);
            color: white;
            border: none;
        }}
        
        QWidget[class="stat-card-warning"] {{
            background: linear-gradient(135deg, {ModernTheme.WARNING_COLOR} 0%, #d97706 100%);
            color: white;
            border: none;
        }}
        
        QWidget[class="stat-card-error"] {{
            background: linear-gradient(135deg, {ModernTheme.ERROR_COLOR} 0%, #dc2626 100%);
            color: white;
            border: none;
        }}
        
        /* Labels de titre */
        QLabel[class="title"] {{
            font-size: 28px;
            font-weight: 700;
            color: {ModernTheme.TEXT_PRIMARY};
            margin-bottom: 8px;
        }}
        
        QLabel[class="subtitle"] {{
            font-size: 18px;
            font-weight: 600;
            color: {ModernTheme.TEXT_SECONDARY};
            margin-bottom: 16px;
        }}
        
        QLabel[class="stat-title"] {{
            font-size: 14px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 8px;
        }}
        
        QLabel[class="stat-value"] {{
            font-size: 32px;
            font-weight: 700;
            color: white;
            margin-bottom: 4px;
        }}
        
        QLabel[class="stat-subtitle"] {{
            font-size: 12px;
            font-weight: 400;
            color: rgba(255, 255, 255, 0.8);
        }}
        
        /* Boutons modernes */
        QPushButton {{
            background-color: {ModernTheme.PRIMARY_COLOR};
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: 500;
            min-height: 20px;
        }}
        
        QPushButton:hover {{
            background-color: #1d4ed8;
        }}

        QPushButton:pressed {{
            background-color: #1e40af;
        }}
        
        QPushButton[class="secondary"] {{
            background-color: {ModernTheme.BACKGROUND_TERTIARY};
            color: {ModernTheme.TEXT_PRIMARY};
            border: 1px solid {ModernTheme.BORDER_COLOR};
        }}
        
        QPushButton[class="secondary"]:hover {{
            background-color: {ModernTheme.BORDER_HOVER};
        }}
        
        QPushButton[class="success"] {{
            background-color: {ModernTheme.SUCCESS_COLOR};
        }}
        
        QPushButton[class="success"]:hover {{
            background-color: #059669;
        }}
        
        QPushButton[class="warning"] {{
            background-color: {ModernTheme.WARNING_COLOR};
        }}
        
        QPushButton[class="warning"]:hover {{
            background-color: #d97706;
        }}
        
        QPushButton[class="danger"] {{
            background-color: {ModernTheme.ERROR_COLOR};
        }}
        
        QPushButton[class="danger"]:hover {{
            background-color: #dc2626;
        }}
        
        /* Tables modernes */
        QTableWidget {{
            background-color: {ModernTheme.BACKGROUND_PRIMARY};
            border: 1px solid {ModernTheme.BORDER_COLOR};
            border-radius: 8px;
            gridline-color: {ModernTheme.BORDER_COLOR};
            selection-background-color: rgba(37, 99, 235, 0.1);
        }}
        
        QTableWidget::item {{
            padding: 12px;
            border-bottom: 1px solid {ModernTheme.BORDER_COLOR};
        }}
        
        QTableWidget::item:selected {{
            background-color: rgba(37, 99, 235, 0.1);
            color: {ModernTheme.TEXT_PRIMARY};
        }}
        
        QHeaderView::section {{
            background-color: {ModernTheme.BACKGROUND_TERTIARY};
            color: {ModernTheme.TEXT_SECONDARY};
            padding: 12px;
            border: none;
            border-bottom: 2px solid {ModernTheme.BORDER_COLOR};
            font-weight: 600;
        }}
        
        /* Champs de saisie */
        QLineEdit, QTextEdit, QSpinBox, QDoubleSpinBox {{
            background-color: {ModernTheme.BACKGROUND_PRIMARY};
            border: 2px solid {ModernTheme.BORDER_COLOR};
            border-radius: 8px;
            padding: 12px;
            font-size: 14px;
            color: {ModernTheme.TEXT_PRIMARY};
        }}
        
        QLineEdit:focus, QTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus {{
            border-color: {ModernTheme.PRIMARY_COLOR};
            outline: none;
        }}
        
        /* ComboBox */
        QComboBox {{
            background-color: {ModernTheme.BACKGROUND_PRIMARY};
            border: 2px solid {ModernTheme.BORDER_COLOR};
            border-radius: 8px;
            padding: 12px;
            font-size: 14px;
            color: {ModernTheme.TEXT_PRIMARY};
            min-width: 120px;
        }}
        
        QComboBox:focus {{
            border-color: {ModernTheme.PRIMARY_COLOR};
        }}
        
        QComboBox::drop-down {{
            border: none;
            width: 30px;
        }}
        
        QComboBox::down-arrow {{
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid {ModernTheme.TEXT_SECONDARY};
        }}
        
        /* Scrollbars */
        QScrollBar:vertical {{
            background-color: {ModernTheme.BACKGROUND_TERTIARY};
            width: 12px;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:vertical {{
            background-color: {ModernTheme.BORDER_HOVER};
            border-radius: 6px;
            min-height: 20px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background-color: {ModernTheme.TEXT_MUTED};
        }}
        
        /* Onglets */
        QTabWidget::pane {{
            border: 1px solid {ModernTheme.BORDER_COLOR};
            border-radius: 8px;
            background-color: {ModernTheme.BACKGROUND_PRIMARY};
        }}
        
        QTabBar::tab {{
            background-color: {ModernTheme.BACKGROUND_TERTIARY};
            color: {ModernTheme.TEXT_SECONDARY};
            padding: 12px 24px;
            margin-right: 4px;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            font-weight: 500;
        }}
        
        QTabBar::tab:selected {{
            background-color: {ModernTheme.PRIMARY_COLOR};
            color: white;
        }}
        
        QTabBar::tab:hover:!selected {{
            background-color: {ModernTheme.BORDER_HOVER};
        }}
        """
    
    @staticmethod
    def get_dark_stylesheet():
        """Retourner le stylesheet sombre"""
        # Version sombre du thème (à implémenter si nécessaire)
        pass
