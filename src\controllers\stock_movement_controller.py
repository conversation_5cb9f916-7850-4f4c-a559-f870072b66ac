"""
Contrôleur pour la gestion des mouvements de stock
Gère les entrées, sorties et ajustements de stock avec traçabilité complète
"""

from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, date
from decimal import Decimal

from .base import BaseController, CRUDResult
from models.stock_movement import StockMovement, MovementType, MovementReason
from utils.logger import setup_logger


class StockMovementController(BaseController):
    """Contrôleur pour les mouvements de stock"""

    def __init__(self, db_manager):
        super().__init__(db_manager)
        self.logger = setup_logger(__name__)
        self.logger.info("StockMovementController initialisé")

    def get_table_name(self):
        """Retourner le nom de la table"""
        return 'stock_movements'
    
    def create_movement(self, movement_data: Dict[str, Any]) -> CRUDResult:
        """Créer un nouveau mouvement de stock"""
        try:
            # Validation des données
            validation_result = self._validate_movement_data(movement_data)
            if not validation_result.success:
                return validation_result
            
            # Créer le mouvement
            movement = StockMovement()
            movement.update_from_dict(movement_data)
            
            # Validation du modèle
            if not movement.is_valid():
                return CRUDResult(False, None, f"Données invalides: {movement.get_validation_errors()}")
            
            # Insérer en base
            cursor = self.db_manager.cursor()
            
            insert_query = """
                INSERT INTO stock_movements (
                    article_id, article_name, article_reference,
                    movement_type, movement_reason, quantity,
                    unit_price, total_value, previous_quantity, new_quantity,
                    supplier_id, supplier_name, order_id, order_number,
                    user_id, user_name, notes, movement_date
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            values = (
                movement.article_id, movement.article_name, movement.article_reference,
                movement.movement_type, movement.movement_reason, movement.quantity,
                float(movement.unit_price), float(movement.total_value),
                movement.previous_quantity, movement.new_quantity,
                movement.supplier_id, movement.supplier_name,
                movement.order_id, movement.order_number,
                movement.user_id, movement.user_name, movement.notes,
                movement.movement_date.isoformat() if movement.movement_date else datetime.now().isoformat()
            )
            
            cursor.execute(insert_query, values)
            movement_id = cursor.lastrowid
            self.db_manager.commit()
            
            self.logger.info(f"Mouvement de stock créé: ID {movement_id}, Article {movement.article_name}, Type {movement.movement_type}")
            return CRUDResult(True, movement_id, "Mouvement de stock créé avec succès")
            
        except Exception as e:
            self.db_manager.rollback()
            self.logger.error(f"Erreur lors de la création du mouvement: {e}")
            return CRUDResult(False, None, f"Erreur lors de la création: {e}")
    
    def get_movements_by_article(self, article_id: int, limit: int = 100) -> List[Dict[str, Any]]:
        """Récupérer les mouvements d'un article spécifique"""
        try:
            cursor = self.db_manager.cursor()
            
            query = """
                SELECT * FROM stock_movements 
                WHERE article_id = ? 
                ORDER BY movement_date DESC, created_at DESC 
                LIMIT ?
            """
            
            cursor.execute(query, (article_id, limit))
            movements = cursor.fetchall()
            
            return [dict(movement) for movement in movements]
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des mouvements: {e}")
            return []
    
    def get_movements_by_date_range(self, start_date: date, end_date: date) -> List[Dict[str, Any]]:
        """Récupérer les mouvements dans une période donnée"""
        try:
            cursor = self.db_manager.cursor()
            
            query = """
                SELECT * FROM stock_movements 
                WHERE DATE(movement_date) BETWEEN ? AND ?
                ORDER BY movement_date DESC, created_at DESC
            """
            
            cursor.execute(query, (start_date.isoformat(), end_date.isoformat()))
            movements = cursor.fetchall()
            
            return [dict(movement) for movement in movements]
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des mouvements par date: {e}")
            return []
    
    def get_movements_by_type(self, movement_type: str) -> List[Dict[str, Any]]:
        """Récupérer les mouvements par type"""
        try:
            cursor = self.db_manager.cursor()
            
            query = """
                SELECT * FROM stock_movements 
                WHERE movement_type = ?
                ORDER BY movement_date DESC, created_at DESC
            """
            
            cursor.execute(query, (movement_type,))
            movements = cursor.fetchall()
            
            return [dict(movement) for movement in movements]
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des mouvements par type: {e}")
            return []
    
    def get_stock_summary(self, article_id: int) -> Dict[str, Any]:
        """Obtenir un résumé des mouvements de stock pour un article"""
        try:
            cursor = self.db_manager.cursor()
            
            # Statistiques générales
            query = """
                SELECT 
                    COUNT(*) as total_movements,
                    SUM(CASE WHEN movement_type = 'Entrée' THEN quantity ELSE 0 END) as total_entries,
                    SUM(CASE WHEN movement_type = 'Sortie' THEN quantity ELSE 0 END) as total_exits,
                    SUM(CASE WHEN movement_type = 'Ajustement' AND quantity > 0 THEN quantity ELSE 0 END) as positive_adjustments,
                    SUM(CASE WHEN movement_type = 'Ajustement' AND quantity < 0 THEN ABS(quantity) ELSE 0 END) as negative_adjustments,
                    MAX(movement_date) as last_movement_date,
                    AVG(unit_price) as average_price
                FROM stock_movements 
                WHERE article_id = ?
            """
            
            cursor.execute(query, (article_id,))
            result = cursor.fetchone()
            
            if result:
                summary = dict(result)
                # Calculer le stock théorique basé sur les mouvements
                summary['theoretical_stock'] = (
                    (summary['total_entries'] or 0) - 
                    (summary['total_exits'] or 0) + 
                    (summary['positive_adjustments'] or 0) - 
                    (summary['negative_adjustments'] or 0)
                )
                return summary
            else:
                return {
                    'total_movements': 0,
                    'total_entries': 0,
                    'total_exits': 0,
                    'positive_adjustments': 0,
                    'negative_adjustments': 0,
                    'theoretical_stock': 0,
                    'last_movement_date': None,
                    'average_price': 0
                }
                
        except Exception as e:
            self.logger.error(f"Erreur lors du calcul du résumé de stock: {e}")
            return {}
    
    def create_entry_movement(self, article_id: int, quantity: int, unit_price: Decimal, 
                            reason: str = "Réception", supplier_id: int = None, 
                            order_id: int = None, notes: str = "") -> CRUDResult:
        """Créer un mouvement d'entrée de stock"""
        try:
            # Récupérer les informations de l'article
            from .article_controller import ArticleController
            article_controller = ArticleController(self.db_manager)
            article_data = article_controller.get_by_id(article_id)
            
            if not article_data:
                return CRUDResult(False, None, "Article non trouvé")
            
            # Récupérer les informations du fournisseur si spécifié
            supplier_name = ""
            if supplier_id:
                from .supplier_controller import SupplierController
                supplier_controller = SupplierController(self.db_manager)
                supplier_data = supplier_controller.get_by_id(supplier_id)
                if supplier_data:
                    supplier_name = supplier_data['name']
            
            # Calculer les nouvelles quantités
            previous_quantity = article_data['quantity_in_stock']
            new_quantity = previous_quantity + quantity
            
            # Créer le mouvement
            movement_data = {
                'article_id': article_id,
                'article_name': article_data['name'],
                'article_reference': article_data['reference'],
                'movement_type': 'Entrée',
                'movement_reason': reason,
                'quantity': quantity,
                'unit_price': unit_price,
                'total_value': unit_price * quantity,
                'previous_quantity': previous_quantity,
                'new_quantity': new_quantity,
                'supplier_id': supplier_id,
                'supplier_name': supplier_name,
                'order_id': order_id,
                'user_id': 1,  # TODO: Récupérer l'utilisateur actuel
                'user_name': 'Système',
                'notes': notes,
                'movement_date': datetime.now()
            }
            
            # Créer le mouvement
            result = self.create_movement(movement_data)
            
            if result.success:
                # Mettre à jour le stock de l'article
                article_controller.update(article_id, {
                    'quantity_in_stock': new_quantity,
                    'unit_price': unit_price  # Mettre à jour le prix si nécessaire
                })
                
                self.logger.info(f"Entrée de stock créée: Article {article_data['name']}, Quantité +{quantity}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la création de l'entrée: {e}")
            return CRUDResult(False, None, f"Erreur lors de la création de l'entrée: {e}")
    
    def create_exit_movement(self, article_id: int, quantity: int, 
                           reason: str = "Vente", notes: str = "") -> CRUDResult:
        """Créer un mouvement de sortie de stock"""
        try:
            # Récupérer les informations de l'article
            from .article_controller import ArticleController
            article_controller = ArticleController(self.db_manager)
            article_data = article_controller.get_by_id(article_id)
            
            if not article_data:
                return CRUDResult(False, None, "Article non trouvé")
            
            # Vérifier le stock disponible
            previous_quantity = article_data['quantity_in_stock']
            if previous_quantity < quantity:
                return CRUDResult(False, None, f"Stock insuffisant. Disponible: {previous_quantity}, Demandé: {quantity}")
            
            # Calculer les nouvelles quantités
            new_quantity = previous_quantity - quantity
            
            # Créer le mouvement
            movement_data = {
                'article_id': article_id,
                'article_name': article_data['name'],
                'article_reference': article_data['reference'],
                'movement_type': 'Sortie',
                'movement_reason': reason,
                'quantity': quantity,
                'unit_price': article_data['unit_price'],
                'total_value': article_data['unit_price'] * quantity,
                'previous_quantity': previous_quantity,
                'new_quantity': new_quantity,
                'user_id': 1,  # TODO: Récupérer l'utilisateur actuel
                'user_name': 'Système',
                'notes': notes,
                'movement_date': datetime.now()
            }
            
            # Créer le mouvement
            result = self.create_movement(movement_data)
            
            if result.success:
                # Mettre à jour le stock de l'article
                article_controller.update(article_id, {
                    'quantity_in_stock': new_quantity
                })
                
                self.logger.info(f"Sortie de stock créée: Article {article_data['name']}, Quantité -{quantity}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la création de la sortie: {e}")
            return CRUDResult(False, None, f"Erreur lors de la création de la sortie: {e}")
    
    def _validate_movement_data(self, data: Dict[str, Any]) -> CRUDResult:
        """Valider les données d'un mouvement"""
        required_fields = ['article_id', 'movement_type', 'quantity']
        
        for field in required_fields:
            if field not in data or data[field] is None:
                return CRUDResult(False, None, f"Champ requis manquant: {field}")
        
        # Validation des types
        if not isinstance(data['article_id'], int) or data['article_id'] <= 0:
            return CRUDResult(False, None, "ID article invalide")
        
        if data['movement_type'] not in ['Entrée', 'Sortie', 'Ajustement']:
            return CRUDResult(False, None, "Type de mouvement invalide")
        
        if not isinstance(data['quantity'], (int, float)) or data['quantity'] <= 0:
            return CRUDResult(False, None, "Quantité invalide")
        
        return CRUDResult(True, None, "Validation réussie")
    
    def get_recent_movements(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Récupérer les mouvements récents"""
        try:
            cursor = self.db_manager.cursor()
            
            query = """
                SELECT * FROM stock_movements 
                ORDER BY movement_date DESC, created_at DESC 
                LIMIT ?
            """
            
            cursor.execute(query, (limit,))
            movements = cursor.fetchall()
            
            return [dict(movement) for movement in movements]
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des mouvements récents: {e}")
            return []
    
    def get_movement_statistics(self) -> Dict[str, Any]:
        """Obtenir des statistiques sur les mouvements"""
        try:
            cursor = self.db_manager.cursor()
            
            # Statistiques générales
            query = """
                SELECT 
                    COUNT(*) as total_movements,
                    COUNT(CASE WHEN movement_type = 'Entrée' THEN 1 END) as total_entries,
                    COUNT(CASE WHEN movement_type = 'Sortie' THEN 1 END) as total_exits,
                    COUNT(CASE WHEN movement_type = 'Ajustement' THEN 1 END) as total_adjustments,
                    SUM(CASE WHEN movement_type = 'Entrée' THEN total_value ELSE 0 END) as entry_value,
                    SUM(CASE WHEN movement_type = 'Sortie' THEN total_value ELSE 0 END) as exit_value,
                    COUNT(CASE WHEN DATE(movement_date) = DATE('now') THEN 1 END) as today_movements,
                    COUNT(CASE WHEN DATE(movement_date) >= DATE('now', '-7 days') THEN 1 END) as week_movements
                FROM stock_movements
            """
            
            cursor.execute(query)
            result = cursor.fetchone()
            
            return dict(result) if result else {}
            
        except Exception as e:
            self.logger.error(f"Erreur lors du calcul des statistiques: {e}")
            return {}
