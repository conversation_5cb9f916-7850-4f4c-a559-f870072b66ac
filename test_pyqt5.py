#!/usr/bin/env python3
"""
Test simple pour vérifier PyQt5
"""

import sys

try:
    from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton
    from PyQt5.QtCore import Qt
    
    print("PyQt5 importé avec succès!")
    
    class TestWindow(QWidget):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("Test PyQt5")
            self.setGeometry(300, 300, 300, 200)
            
            layout = QVBoxLayout()
            
            label = QLabel("PyQt5 fonctionne!")
            label.setAlignment(Qt.AlignCenter)
            layout.addWidget(label)
            
            button = QPushButton("Fermer")
            button.clicked.connect(self.close)
            layout.addWidget(button)
            
            self.setLayout(layout)
    
    if __name__ == "__main__":
        app = QApplication(sys.argv)
        window = TestWindow()
        window.show()
        sys.exit(app.exec_())
        
except ImportError as e:
    print(f"Erreur d'import PyQt5: {e}")
    print("Veuillez installer PyQt5 avec: pip install PyQt5")
    sys.exit(1)
