#!/usr/bin/env python3
"""
Test du module de gestion des fournisseurs
"""

import sys
from pathlib import Path

# Ajouter le répertoire src au path Python
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_supplier_model():
    """Tester le modèle Supplier"""
    print("🧪 Test du modèle Supplier...")
    
    try:
        from models.supplier import Supplier, create_supplier_from_dict
        from models.base import ValidationError
        
        # Test création supplier valide
        supplier_data = {
            'name': 'Test Supplier',
            'company_name': 'Test Company SARL',
            'contact_person': 'Jean Test',
            'email': '<EMAIL>',
            'phone': '0123456789',
            'address': '123 Rue Test',
            'city': 'Paris',
            'postal_code': '75001',
            'rating': 4
        }
        
        supplier = Supplier(**supplier_data)
        if supplier.validate():
            print("  ✅ Modèle Supplier validé")
            print(f"  ✅ Nom complet: {supplier.get_full_name()}")
            print(f"  ✅ Contact principal: {supplier.get_primary_contact()}")
            print(f"  ✅ Adresse complète: {supplier.get_full_address()}")
            print(f"  ✅ Note étoiles: {supplier.get_rating_stars()}")
            print(f"  ✅ Infos paiement: {supplier.get_payment_info()}")
        else:
            print(f"  ❌ Erreurs Supplier: {supplier.get_errors()}")
            return False
        
        # Test validation email invalide
        invalid_supplier = Supplier(name="Test", email="email-invalide")
        if not invalid_supplier.validate():
            print("  ✅ Validation email invalide détectée")
        else:
            print("  ❌ Validation email invalide non détectée")
        
        # Test fonction utilitaire
        try:
            valid_supplier = create_supplier_from_dict(supplier_data)
            print("  ✅ Fonction create_supplier_from_dict fonctionne")
        except ValidationError as e:
            print(f"  ❌ Erreur create_supplier_from_dict: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur modèle Supplier: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_supplier_controller():
    """Tester le contrôleur Supplier"""
    print("\n🎮 Test du contrôleur Supplier...")
    
    try:
        from database.manager import DatabaseManager
        from controllers.supplier_controller import SupplierController
        
        # Initialiser la base de données
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        supplier_controller = SupplierController(db_manager)
        
        # Créer un fournisseur de test
        supplier_data = {
            'name': 'Fournisseur Test Controller',
            'company_name': 'Test Controller SARL',
            'contact_person': 'Marie Controller',
            'email': '<EMAIL>',
            'phone': '0987654321',
            'address': '456 Avenue Controller',
            'city': 'Lyon',
            'postal_code': '69000',
            'rating': 5,
            'category': 'Test',
            'payment_terms': 45,
            'discount_rate': 5.0
        }
        
        # Test création
        result = supplier_controller.create_supplier(supplier_data)
        if result.success:
            print("  ✅ Fournisseur créé via contrôleur")
            supplier_id = result.data['id']
            
            # Test récupération avec détails
            supplier = supplier_controller.get_supplier_with_details(supplier_id)
            if supplier:
                print(f"  ✅ Fournisseur récupéré: {supplier['name']}")
                print(f"  ✅ Statistiques: {supplier['article_count']} articles")
                print(f"  ✅ Note: {supplier['rating_stars']}")
            
            # Test mise à jour note
            rating_result = supplier_controller.update_rating(supplier_id, 3)
            if rating_result.success:
                print("  ✅ Note mise à jour")
            
            # Test activation/désactivation
            deactivate_result = supplier_controller.deactivate_supplier(supplier_id)
            if deactivate_result.success:
                print("  ✅ Fournisseur désactivé")
            
            activate_result = supplier_controller.activate_supplier(supplier_id)
            if activate_result.success:
                print("  ✅ Fournisseur réactivé")
            
            # Test recherche
            search_results = supplier_controller.search_suppliers("Controller")
            if search_results:
                print(f"  ✅ Recherche: {len(search_results)} résultat(s)")
            
            # Test top fournisseurs
            top_suppliers = supplier_controller.get_top_suppliers(5)
            print(f"  ✅ Top fournisseurs: {len(top_suppliers)}")
            
        else:
            print(f"  ❌ Erreur création fournisseur: {result.message}")
            return False
        
        db_manager.close()
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur contrôleur Supplier: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_supplier_interface():
    """Tester l'interface fournisseurs"""
    print("\n🖼️ Test de l'interface fournisseurs...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from views.suppliers_window import SuppliersWindow, SupplierDialog
        
        # Mock de l'instance app
        class MockApp:
            def get_database_manager(self):
                from database.manager import DatabaseManager
                db = DatabaseManager()
                db.initialize_database()
                return db
        
        app = QApplication([])
        mock_app = MockApp()
        
        # Créer la fenêtre fournisseurs
        suppliers_window = SuppliersWindow(mock_app)
        print("  ✅ Fenêtre fournisseurs créée")
        
        # Tester le chargement
        suppliers_window._load_suppliers()
        print(f"  ✅ Fournisseurs chargés: {len(suppliers_window.current_suppliers)}")
        
        # Tester le dialog
        dialog = SupplierDialog(suppliers_window, "Test Dialog")
        print("  ✅ Dialog fournisseur créé")
        
        # Tester les données du dialog
        test_data = dialog.get_supplier_data()
        if isinstance(test_data, dict):
            print("  ✅ Récupération données dialog fonctionne")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur interface fournisseurs: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration():
    """Test d'intégration fournisseurs-articles"""
    print("\n🔗 Test d'intégration fournisseurs-articles...")
    
    try:
        from database.manager import DatabaseManager
        from controllers.supplier_controller import SupplierController
        from controllers.article_controller import ArticleController
        
        # Initialiser la base de données
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        supplier_controller = SupplierController(db_manager)
        article_controller = ArticleController(db_manager)
        
        # Créer un fournisseur
        supplier_data = {
            'name': 'Fournisseur Intégration',
            'email': '<EMAIL>'
        }
        
        supplier_result = supplier_controller.create_supplier(supplier_data)
        if not supplier_result.success:
            print(f"  ❌ Erreur création fournisseur: {supplier_result.message}")
            return False
        
        supplier_id = supplier_result.data['id']
        
        # Créer un article associé
        article_data = {
            'name': 'Article Test Intégration',
            'reference': 'INT-001',
            'supplier_id': supplier_id,
            'supplier_name': 'Fournisseur Intégration',
            'unit_price': 25.99,
            'quantity_in_stock': 10,
            'minimum_stock': 2
        }
        
        article_result = article_controller.create_article(article_data)
        if not article_result.success:
            print(f"  ❌ Erreur création article: {article_result.message}")
            return False
        
        print("  ✅ Fournisseur et article créés")
        
        # Vérifier l'association
        supplier_with_details = supplier_controller.get_supplier_with_details(supplier_id)
        if supplier_with_details and supplier_with_details['article_count'] > 0:
            print("  ✅ Association fournisseur-article détectée")
        else:
            print("  ❌ Association fournisseur-article non détectée")
            return False
        
        # Tester la contrainte de suppression
        success, message = supplier_controller.delete(supplier_id)
        if not success and "article" in message.lower():
            print("  ✅ Contrainte de suppression fonctionne")
        else:
            print("  ❌ Contrainte de suppression ne fonctionne pas")
            return False
        
        db_manager.close()
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur intégration: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale de test"""
    print("🧪 Tests du module Fournisseurs GSlim")
    print("=" * 60)
    
    tests = [
        ("Modèle Supplier", test_supplier_model),
        ("Contrôleur Supplier", test_supplier_controller),
        ("Interface Fournisseurs", test_supplier_interface),
        ("Intégration", test_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur dans {test_name}: {e}")
            results.append((test_name, False))
    
    # Résumé
    print("\n" + "=" * 60)
    print("📊 Résumé des tests:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHEC"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Résultat: {passed}/{len(tests)} tests réussis")
    
    if passed == len(tests):
        print("🎉 Module Fournisseurs prêt !")
        print("\n💡 Pour tester l'interface complète:")
        print("   python main.py")
        print("   Puis naviguez vers 'Fournisseurs' dans le menu")
    else:
        print("⚠️ Certains tests ont échoué.")
    
    return passed == len(tests)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
