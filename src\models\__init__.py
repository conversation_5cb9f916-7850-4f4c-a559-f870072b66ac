"""
Module des modèles de données pour GSlim
Contient tous les modèles métier avec validation et méthodes business
"""

from .base import BaseModel, TimestampMixin, ValidationError
from .article import Article, create_article_from_dict, calculate_selling_price_from_cost, calculate_cost_from_selling_price
from .supplier import Supplier, create_supplier_from_dict, get_payment_methods, get_supplier_categories
from .stock_movement import (
    StockMovement, MovementType, MovementReason,
    create_movement_from_dict, get_movement_types, get_movement_reasons,
    get_entry_reasons, get_exit_reasons
)

__all__ = [
    # Classes de base
    'BaseModel',
    'TimestampMixin',
    'ValidationError',

    # Modèles
    'Article',
    'Supplier',
    'StockMovement',

    # Enums
    'MovementType',
    'MovementReason',

    # Fonctions utilitaires Articles
    'create_article_from_dict',
    'calculate_selling_price_from_cost',
    'calculate_cost_from_selling_price',

    # Fonctions utilitaires Fournisseurs
    'create_supplier_from_dict',
    'get_payment_methods',
    'get_supplier_categories',

    # Fonctions utilitaires Mouvements
    'create_movement_from_dict',
    'get_movement_types',
    'get_movement_reasons',
    'get_entry_reasons',
    'get_exit_reasons'
]
