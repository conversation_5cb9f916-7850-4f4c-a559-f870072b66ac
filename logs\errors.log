2025-07-31 20:40:04 - src.app - ERROR - Erreur lors de l'ouverture de la fenêtre principale: type object 'FluentIcon' has no attribute 'PACKAGE'
2025-07-31 20:41:08 - src.app - ERROR - Erreur lors de l'ouverture de la fenêtre principale: type object 'FluentIcon' has no attribute 'PACKAGE'
2025-07-31 20:43:54 - src.app - ERROR - Erreur lors de l'ouverture de la fenêtre principale: type object 'FluentIcon' has no attribute 'PACKAGE'
2025-07-31 21:06:14 - ArticleController - ERROR - Erreur lors de la création dans articles: table articles has no column named category_name
2025-07-31 21:06:14 - SupplierController - ERROR - Erreur lors de la création dans suppliers: table suppliers has no column named company_name
2025-07-31 21:06:15 - ArticleController - ERROR - Erreur lors du calcul des statistiques: no such column: quantity_in_stock
2025-07-31 21:11:57 - ArticleController - ERROR - Erreur lors de la création dans articles: Error binding parameter 16: type 'decimal.Decimal' is not supported
2025-07-31 21:18:31 - src.app - ERROR - Erreur lors de l'ouverture de la fenêtre principale: type object 'FluentIcon' has no attribute 'PACKAGE'
2025-07-31 21:56:49 - src.app - ERROR - Erreur lors de l'ouverture de la fenêtre principale: type object 'FluentIcon' has no attribute 'PACKAGE'
2025-08-01 16:42:36 - src.app - ERROR - Erreur lors de l'ouverture de la fenêtre principale: type object 'FluentIcon' has no attribute 'PACKAGE'
2025-08-01 16:42:45 - src.app - ERROR - Erreur lors de l'ouverture de la fenêtre principale: type object 'FluentIcon' has no attribute 'PACKAGE'
2025-08-01 16:45:11 - src.app - ERROR - Erreur lors de l'ouverture de la fenêtre principale: type object 'FluentIcon' has no attribute 'PACKAGE'
2025-08-01 17:27:55 - src.app - ERROR - Erreur lors de l'ouverture de la fenêtre principale: type object 'FluentIcon' has no attribute 'SIGN_OUT'
2025-08-01 17:30:43 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: wrapped C/C++ object of type ArticlesWindow has been deleted
2025-08-01 17:32:07 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: wrapped C/C++ object of type SuppliersWindow has been deleted
2025-08-01 17:32:12 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: wrapped C/C++ object of type SuppliersWindow has been deleted
2025-08-01 17:32:12 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: wrapped C/C++ object of type ArticlesWindow has been deleted
2025-08-01 18:06:07 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: Can't instantiate abstract class StockMovementController without an implementation for abstract method 'get_table_name'
2025-08-01 18:06:12 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: Can't instantiate abstract class StockMovementController without an implementation for abstract method 'get_table_name'
2025-08-01 18:06:18 - views.main_window - ERROR - Erreur lors de l'affichage du module mouvements: Can't instantiate abstract class StockMovementController without an implementation for abstract method 'get_table_name'
2025-08-01 18:06:19 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: Can't instantiate abstract class OrderController without an implementation for abstract method 'get_table_name'
2025-08-01 18:06:19 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: Can't instantiate abstract class ReportController without an implementation for abstract method 'get_table_name'
2025-08-01 18:09:07 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: BaseController.__init__() takes 2 positional arguments but 3 were given
2025-08-01 18:09:14 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: BaseController.__init__() takes 2 positional arguments but 3 were given
2025-08-01 18:09:18 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: wrapped C/C++ object of type SuppliersWindow has been deleted
2025-08-01 18:09:19 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: wrapped C/C++ object of type ArticlesWindow has been deleted
2025-08-01 18:09:22 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: BaseController.__init__() takes 2 positional arguments but 3 were given
2025-08-01 18:09:24 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: wrapped C/C++ object of type ArticlesWindow has been deleted
2025-08-01 18:09:24 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: wrapped C/C++ object of type SuppliersWindow has been deleted
2025-08-01 18:09:25 - views.main_window - ERROR - Erreur lors de l'affichage du module mouvements: BaseController.__init__() takes 2 positional arguments but 3 were given
2025-08-01 18:09:25 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: BaseController.__init__() takes 2 positional arguments but 3 were given
2025-08-01 18:09:26 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: BaseController.__init__() takes 2 positional arguments but 3 were given
2025-08-01 18:09:28 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: BaseController.__init__() takes 2 positional arguments but 3 were given
2025-08-01 18:09:33 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: BaseController.__init__() takes 2 positional arguments but 3 were given
2025-08-01 18:10:27 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: BaseController.__init__() takes 2 positional arguments but 3 were given
2025-08-01 18:10:31 - views.main_window - ERROR - Erreur lors de l'affichage du module mouvements: BaseController.__init__() takes 2 positional arguments but 3 were given
2025-08-01 18:10:35 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: BaseController.__init__() takes 2 positional arguments but 3 were given
2025-08-01 18:10:36 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: BaseController.__init__() takes 2 positional arguments but 3 were given
2025-08-01 18:10:39 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: wrapped C/C++ object of type SuppliersWindow has been deleted
2025-08-01 18:10:40 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: BaseController.__init__() takes 2 positional arguments but 3 were given
2025-08-01 18:10:41 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: wrapped C/C++ object of type ArticlesWindow has been deleted
2025-08-01 18:10:42 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: wrapped C/C++ object of type SuppliersWindow has been deleted
2025-08-01 18:10:43 - views.main_window - ERROR - Erreur lors de l'affichage du module mouvements: BaseController.__init__() takes 2 positional arguments but 3 were given
2025-08-01 18:10:44 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: BaseController.__init__() takes 2 positional arguments but 3 were given
2025-08-01 18:13:51 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'SupplierController' object has no attribute 'get_supplier_statistics'
2025-08-01 18:14:13 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'get_cursor'
2025-08-01 18:14:13 - views.main_window - ERROR - Erreur lors de l'affichage du module mouvements: 'ComboBox' object has no attribute 'setEditable'
2025-08-01 18:14:18 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'get_cursor'
2025-08-01 18:14:18 - views.main_window - ERROR - Erreur lors de l'affichage du module mouvements: 'ComboBox' object has no attribute 'setEditable'
2025-08-01 18:14:19 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'get_cursor'
2025-08-01 18:14:19 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_apply_filters'
2025-08-01 18:14:20 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'CHART'
2025-08-01 18:14:21 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: wrapped C/C++ object of type DashboardWindow has been deleted
2025-08-01 18:14:23 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: wrapped C/C++ object of type DashboardWindow has been deleted
2025-08-01 18:27:37 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'SupplierController' object has no attribute 'get_supplier_statistics'
2025-08-01 18:27:57 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: wrapped C/C++ object of type ArticlesWindow has been deleted
2025-08-01 18:27:58 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: wrapped C/C++ object of type SuppliersWindow has been deleted
2025-08-01 18:27:59 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: wrapped C/C++ object of type SuppliersWindow has been deleted
2025-08-01 18:27:59 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'get_cursor'
2025-08-01 18:28:00 - views.main_window - ERROR - Erreur lors de l'affichage du module mouvements: 'ComboBox' object has no attribute 'setEditable'
2025-08-01 18:28:00 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'get_cursor'
2025-08-01 18:28:00 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_apply_filters'
2025-08-01 18:28:01 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'CHART'
2025-08-01 18:28:01 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: wrapped C/C++ object of type ArticlesWindow has been deleted
2025-08-01 18:28:02 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: wrapped C/C++ object of type DashboardWindow has been deleted
2025-08-01 18:29:45 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'SupplierController' object has no attribute 'get_supplier_statistics'
2025-08-01 18:29:53 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: wrapped C/C++ object of type DashboardWindow has been deleted
2025-08-01 18:29:54 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: wrapped C/C++ object of type DashboardWindow has been deleted
2025-08-01 18:29:56 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: wrapped C/C++ object of type ArticlesWindow has been deleted
2025-08-01 18:29:56 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: wrapped C/C++ object of type ArticlesWindow has been deleted
2025-08-01 18:29:57 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'get_cursor'
2025-08-01 18:29:57 - views.main_window - ERROR - Erreur lors de l'affichage du module mouvements: 'ComboBox' object has no attribute 'setEditable'
2025-08-01 18:29:58 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'get_cursor'
2025-08-01 18:29:58 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_apply_filters'
2025-08-01 18:29:59 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'get_cursor'
2025-08-01 18:29:59 - views.main_window - ERROR - Erreur lors de l'affichage du module mouvements: 'ComboBox' object has no attribute 'setEditable'
2025-08-01 18:30:00 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'get_cursor'
2025-08-01 18:30:00 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_apply_filters'
2025-08-01 18:30:01 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'CHART'
2025-08-01 18:42:47 - SupplierController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 18:42:47 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 18:42:47 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 18:42:47 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 18:42:47 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 18:42:55 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: wrapped C/C++ object of type DashboardWindow has been deleted
2025-08-01 18:42:57 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: wrapped C/C++ object of type ArticlesWindow has been deleted
2025-08-01 18:43:00 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 18:43:01 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 18:43:01 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 18:43:01 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 18:43:01 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 18:43:01 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-01 18:43:02 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'CHART'
2025-08-01 18:43:02 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 18:43:02 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-01 18:43:03 - views.main_window - ERROR - Erreur lors de l'affichage du module mouvements: wrapped C/C++ object of type MovementsWindow has been deleted
2025-08-01 18:43:03 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: wrapped C/C++ object of type SuppliersWindow has been deleted
2025-08-01 18:43:04 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: wrapped C/C++ object of type ArticlesWindow has been deleted
2025-08-01 18:43:05 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: wrapped C/C++ object of type DashboardWindow has been deleted
2025-08-01 19:30:28 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:28 - SupplierController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:28 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:28 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:28 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:28 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:28 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:31 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:33 - SupplierController - ERROR - Erreur lors de la récupération des top fournisseurs: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:33 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:33 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:33 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:33 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:33 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:34 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:35 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:35 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:35 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:36 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:36 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-01 19:30:37 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'PIE'
2025-08-01 19:31:09 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:31:09 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-01 19:31:11 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'PIE'
2025-08-01 19:32:27 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:27 - SupplierController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:27 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:27 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:27 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:27 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:27 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:33 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:39 - SupplierController - ERROR - Erreur lors de la récupération des top fournisseurs: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:39 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:39 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:39 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:39 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:39 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:39 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:39 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:39 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:39 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:43 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:43 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-01 19:32:45 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'PIE'
2025-08-01 19:32:59 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:59 - SupplierController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:59 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:59 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:59 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:59 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:59 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:40 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:40 - SupplierController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:40 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:40 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:40 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:40 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:40 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:48 - SupplierController - ERROR - Erreur lors de la récupération des top fournisseurs: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:48 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:48 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:48 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:48 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:48 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:53 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:53 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:53 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:53 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:53 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:55 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:55 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:55 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:55 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:55 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:00 - SupplierController - ERROR - Erreur lors de la vérification d'email: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:02 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:02 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:02 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:02 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:02 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:11 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:11 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:11 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:11 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:11 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:13 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:13 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:13 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:13 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:13 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:15 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:15 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:15 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:15 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:15 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:16 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:16 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:16 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:16 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:16 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:18 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:18 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:18 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:18 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:18 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:23 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:23 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:23 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:23 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:23 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:27 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:35:00 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:35:00 - SupplierController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:35:00 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:35:00 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:35:00 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:35:00 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:35:00 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:32 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:32 - SupplierController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:32 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:32 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:32 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:32 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:32 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:36 - SupplierController - ERROR - Erreur lors de la récupération des top fournisseurs: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:36 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:36 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:36 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:36 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:36 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:37 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:37 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:37 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:37 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:45 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:45 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-01 19:36:47 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'PIE'
2025-08-01 19:36:48 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:48 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-01 19:36:49 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'PIE'
2025-08-01 19:39:16 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:39:16 - SupplierController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:39:16 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:39:16 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:39:16 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:39:16 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:39:16 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:39:19 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:39:20 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:39:20 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:39:20 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:39:21 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:39:21 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-01 19:39:22 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'PIE'
2025-08-01 19:39:23 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:23 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:23 - SupplierController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:23 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:23 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:23 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:23 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:23 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:26 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:26 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:26 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:26 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:27 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:27 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-01 19:41:29 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'PIE'
2025-08-01 19:41:30 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:30 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:30 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:30 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:30 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:30 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:30 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:30 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:30 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:30 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:31 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:21 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:21 - SupplierController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:21 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:21 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:21 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:21 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:21 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:22 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:23 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:23 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:23 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:24 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:24 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-01 19:43:26 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'PIE'
2025-08-01 19:43:28 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:28 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:28 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:28 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:28 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:28 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:28 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:28 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:28 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:28 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:30 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:04 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:04 - SupplierController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:04 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:04 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:04 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:04 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:04 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:07 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:07 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:07 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:07 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:09 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:09 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-01 19:45:11 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'PIE'
2025-08-01 19:45:13 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:13 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:13 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:13 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:13 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:13 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:13 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:13 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:13 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:13 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:15 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:21:53 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: 'DashboardWindow' object has no attribute 'stats_data'
2025-08-01 20:21:57 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: 'DashboardWindow' object has no attribute 'stats_data'
2025-08-01 20:21:58 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:22:01 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:22:01 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:22:01 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:22:01 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:22:01 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:22:01 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:22:01 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:22:01 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:22:01 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:22:01 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:22:11 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: 'DashboardWindow' object has no attribute 'stats_data'
2025-08-01 20:22:13 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: 'DashboardWindow' object has no attribute 'stats_data'
2025-08-01 20:27:48 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: 'DashboardWindow' object has no attribute 'stats_data'
2025-08-01 20:28:04 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: 'DashboardWindow' object has no attribute 'stats_data'
2025-08-01 20:28:05 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-01 20:28:09 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: 'DashboardWindow' object has no attribute 'stats_data'
2025-08-01 20:30:33 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-01 20:30:33 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:30:33 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:30:34 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: name 'QSpacerItem' is not defined
2025-08-01 20:33:16 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-01 20:33:16 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:33:16 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:33:16 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: name 'QSpacerItem' is not defined
2025-08-01 20:37:57 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-01 20:37:57 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:37:57 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:37:57 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-01 20:37:57 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-01 20:38:06 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-01 20:38:08 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:38:09 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:38:09 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:38:09 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:38:10 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:38:10 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-01 20:38:11 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'PIE'
2025-08-01 20:40:37 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-01 20:40:37 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:40:37 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:40:37 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-01 20:40:37 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-01 20:40:40 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:40:41 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:40:41 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:40:41 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:40:42 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:40:42 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-01 20:40:45 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'PIE'
2025-08-02 10:11:46 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 10:11:46 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:11:46 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:11:46 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 10:11:46 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-02 10:11:53 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 10:11:55 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:11:55 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:11:55 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:11:55 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:11:57 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:11:57 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-02 10:11:58 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'PIE'
2025-08-02 10:21:09 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 10:21:09 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:21:09 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:21:09 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 10:21:09 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-02 10:22:37 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:22:37 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:22:37 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:22:37 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:22:42 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:22:42 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
