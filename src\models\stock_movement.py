"""
Modèle Mouvement de Stock pour le suivi des entrées/sorties
"""

from datetime import datetime
from typing import Dict, Any, Optional, List
from enum import Enum

from .base import BaseModel, TimestampMixin, validate_positive_number, validate_non_empty_string


class MovementType(Enum):
    """Types de mouvements de stock"""
    ENTRY = "Entrée"
    EXIT = "Sortie"
    ADJUSTMENT = "Ajustement"
    TRANSFER = "Transfert"
    RETURN = "Retour"
    LOSS = "Perte"
    FOUND = "Trouvé"


class MovementReason(Enum):
    """Raisons des mouvements de stock"""
    # Entrées
    PURCHASE = "Achat"
    PRODUCTION = "Production"
    RETURN_FROM_CUSTOMER = "Retour client"
    ADJUSTMENT_INCREASE = "Ajustement positif"
    TRANSFER_IN = "Transfert entrant"
    FOUND_INVENTORY = "Inventaire - trouvé"
    
    # Sorties
    SALE = "Vente"
    CONSUMPTION = "Consommation"
    RETURN_TO_SUPPLIER = "Retour fournisseur"
    ADJUSTMENT_DECREASE = "Ajustement négatif"
    TRANSFER_OUT = "Transfert sortant"
    LOSS_DAMAGE = "Perte/Dommage"
    LOSS_THEFT = "Vol"
    LOSS_EXPIRY = "Péremption"
    SAMPLE = "Échantillon"
    DESTRUCTION = "Destruction"


class StockMovement(BaseModel, TimestampMixin):
    """Modèle pour les mouvements de stock"""
    
    def _set_defaults(self):
        """Définir les valeurs par défaut"""
        self._data.update({
            'id': None,
            'article_id': None,
            'article_name': '',
            'article_reference': '',
            'movement_type': MovementType.ENTRY.value,
            'movement_reason': MovementReason.PURCHASE.value,
            'quantity': 0,
            'unit_price': 0.0,
            'total_value': 0.0,
            'previous_quantity': 0,
            'new_quantity': 0,
            'user_id': None,
            'user_name': '',
            'document_reference': '',
            'supplier_id': None,
            'supplier_name': '',
            'customer_id': None,
            'customer_name': '',
            'location_from': '',
            'location_to': '',
            'notes': '',
            'is_validated': False,
            'validated_by': None,
            'validated_at': None,
            'movement_date': datetime.now()
        })
        self._set_timestamp_defaults()
    
    def get_validation_rules(self) -> Dict[str, Dict]:
        """Règles de validation pour les mouvements de stock"""
        return {
            'article_id': {
                'required': True,
                'type': int,
                'validator_message': "L'article est requis"
            },
            'movement_type': {
                'required': True,
                'type': str,
                'validator': lambda x: x in [t.value for t in MovementType],
                'validator_message': "Type de mouvement invalide"
            },
            'movement_reason': {
                'required': True,
                'type': str,
                'validator': lambda x: x in [r.value for r in MovementReason],
                'validator_message': "Raison de mouvement invalide"
            },
            'quantity': {
                'required': True,
                'type': int,
                'validator': lambda x: x != 0,
                'validator_message': "La quantité ne peut pas être zéro"
            },
            'unit_price': {
                'type': (float, int),
                'min_value': 0,
                'validator': validate_positive_number,
                'validator_message': "Le prix unitaire doit être positif"
            },
            'movement_date': {
                'required': True,
                'type': datetime,
                'validator_message': "Date de mouvement requise"
            },
            'user_id': {
                'required': True,
                'type': int,
                'validator_message': "Utilisateur requis"
            }
        }
    
    # Propriétés calculées
    def is_entry(self) -> bool:
        """Vérifier si c'est une entrée"""
        return self._data['movement_type'] == MovementType.ENTRY.value
    
    def is_exit(self) -> bool:
        """Vérifier si c'est une sortie"""
        return self._data['movement_type'] == MovementType.EXIT.value
    
    def is_adjustment(self) -> bool:
        """Vérifier si c'est un ajustement"""
        return self._data['movement_type'] == MovementType.ADJUSTMENT.value
    
    def get_signed_quantity(self) -> int:
        """Obtenir la quantité signée (positive pour entrée, négative pour sortie)"""
        quantity = self._data['quantity']
        if self.is_exit():
            return -quantity
        return quantity
    
    def get_movement_impact(self) -> str:
        """Obtenir l'impact du mouvement"""
        signed_qty = self.get_signed_quantity()
        if signed_qty > 0:
            return f"+{signed_qty}"
        else:
            return str(signed_qty)
    
    def get_movement_color(self) -> str:
        """Obtenir la couleur associée au type de mouvement"""
        if self.is_entry():
            return "#28a745"  # Vert
        elif self.is_exit():
            return "#dc3545"  # Rouge
        else:
            return "#6f42c1"  # Violet pour ajustements
    
    def calculate_total_value(self) -> float:
        """Calculer la valeur totale du mouvement"""
        return abs(self._data['quantity']) * self._data['unit_price']
    
    def get_document_info(self) -> str:
        """Obtenir les informations du document"""
        doc_ref = self._data['document_reference']
        if doc_ref:
            return f"Réf: {doc_ref}"
        return "Aucune référence"
    
    def get_partner_info(self) -> str:
        """Obtenir les informations du partenaire (fournisseur/client)"""
        if self._data['supplier_name']:
            return f"Fournisseur: {self._data['supplier_name']}"
        elif self._data['customer_name']:
            return f"Client: {self._data['customer_name']}"
        return "Aucun partenaire"
    
    def get_location_info(self) -> str:
        """Obtenir les informations de localisation"""
        location_from = self._data['location_from']
        location_to = self._data['location_to']
        
        if location_from and location_to:
            return f"De: {location_from} → Vers: {location_to}"
        elif location_from:
            return f"De: {location_from}"
        elif location_to:
            return f"Vers: {location_to}"
        return "Aucune localisation"
    
    # Méthodes de gestion
    def validate_movement(self, user_id: int, user_name: str):
        """Valider le mouvement"""
        if not self._data['is_validated']:
            self._data['is_validated'] = True
            self._data['validated_by'] = user_id
            self._data['validated_at'] = datetime.now()
            self.touch()
            self.logger.info(f"Mouvement validé par {user_name}: {self.get_movement_summary()}")
    
    def cancel_validation(self):
        """Annuler la validation"""
        if self._data['is_validated']:
            self._data['is_validated'] = False
            self._data['validated_by'] = None
            self._data['validated_at'] = None
            self.touch()
            self.logger.info(f"Validation annulée: {self.get_movement_summary()}")
    
    def update_quantities(self, previous_qty: int, new_qty: int):
        """Mettre à jour les quantités avant/après"""
        self._data['previous_quantity'] = previous_qty
        self._data['new_quantity'] = new_qty
        self._data['total_value'] = self.calculate_total_value()
        self.touch()
    
    def get_movement_summary(self) -> str:
        """Obtenir un résumé du mouvement"""
        article = self._data['article_name'] or self._data['article_reference']
        movement_type = self._data['movement_type']
        quantity = self.get_movement_impact()
        reason = self._data['movement_reason']
        
        return f"{movement_type}: {quantity} {article} ({reason})"
    
    # Méthodes de recherche et filtrage
    def matches_search(self, search_term: str) -> bool:
        """Vérifier si le mouvement correspond à un terme de recherche"""
        search_term = search_term.lower()
        searchable_fields = [
            self._data['article_name'],
            self._data['article_reference'],
            self._data['movement_reason'],
            self._data['document_reference'],
            self._data['supplier_name'],
            self._data['customer_name'],
            self._data['user_name'],
            self._data['notes']
        ]
        
        return any(search_term in str(field).lower() for field in searchable_fields if field)
    
    def matches_date_range(self, start_date: datetime, end_date: datetime) -> bool:
        """Vérifier si le mouvement est dans une plage de dates"""
        movement_date = self._data['movement_date']
        if isinstance(movement_date, str):
            movement_date = datetime.fromisoformat(movement_date)
        
        return start_date <= movement_date <= end_date
    
    def matches_filters(self, filters: Dict[str, Any]) -> bool:
        """Vérifier si le mouvement correspond aux filtres"""
        for field, value in filters.items():
            if field in self._data and self._data[field] != value:
                return False
        return True
    
    def to_display_dict(self) -> Dict[str, Any]:
        """Convertir en dictionnaire pour l'affichage"""
        data = self.to_dict()
        data.update({
            'signed_quantity': self.get_signed_quantity(),
            'movement_impact': self.get_movement_impact(),
            'movement_color': self.get_movement_color(),
            'calculated_total_value': self.calculate_total_value(),
            'formatted_total_value': f"{self.calculate_total_value():.2f} €",
            'formatted_unit_price': f"{self._data['unit_price']:.2f} €",
            'document_info': self.get_document_info(),
            'partner_info': self.get_partner_info(),
            'location_info': self.get_location_info(),
            'movement_summary': self.get_movement_summary(),
            'validation_status': "Validé" if self._data['is_validated'] else "En attente",
            'formatted_movement_date': self._data['movement_date'].strftime("%d/%m/%Y %H:%M") if isinstance(self._data['movement_date'], datetime) else self._data['movement_date']
        })
        return data


# Fonctions utilitaires pour les mouvements de stock
def create_movement_from_dict(data: Dict[str, Any]) -> StockMovement:
    """Créer un mouvement à partir d'un dictionnaire"""
    movement = StockMovement(**data)
    if not movement.validate():
        from .base import ValidationError
        raise ValidationError(movement.get_errors())
    return movement


def get_movement_types() -> List[str]:
    """Obtenir la liste des types de mouvements"""
    return [t.value for t in MovementType]


def get_movement_reasons() -> List[str]:
    """Obtenir la liste des raisons de mouvements"""
    return [r.value for r in MovementReason]


def get_entry_reasons() -> List[str]:
    """Obtenir les raisons d'entrée"""
    return [
        MovementReason.PURCHASE.value,
        MovementReason.PRODUCTION.value,
        MovementReason.RETURN_FROM_CUSTOMER.value,
        MovementReason.ADJUSTMENT_INCREASE.value,
        MovementReason.TRANSFER_IN.value,
        MovementReason.FOUND_INVENTORY.value
    ]


def get_exit_reasons() -> List[str]:
    """Obtenir les raisons de sortie"""
    return [
        MovementReason.SALE.value,
        MovementReason.CONSUMPTION.value,
        MovementReason.RETURN_TO_SUPPLIER.value,
        MovementReason.ADJUSTMENT_DECREASE.value,
        MovementReason.TRANSFER_OUT.value,
        MovementReason.LOSS_DAMAGE.value,
        MovementReason.LOSS_THEFT.value,
        MovementReason.LOSS_EXPIRY.value,
        MovementReason.SAMPLE.value,
        MovementReason.DESTRUCTION.value
    ]
