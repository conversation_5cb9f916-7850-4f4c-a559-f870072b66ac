"""
Contrôleur pour la gestion des catégories d'articles
"""

from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

from controllers.base import BaseController, CRUDResult
from models.category import Category, CategoryHierarchy
from utils.logger import setup_logger


class CategoryController(BaseController):
    """Contrôleur pour les catégories d'articles"""
    
    def __init__(self, db_manager):
        super().__init__(db_manager)
        self.logger = setup_logger(__name__)
        self.logger.info("CategoryController initialisé")
    
    def get_table_name(self):
        """Retourner le nom de la table"""
        return 'categories'
    
    def create_record(self, data: Dict[str, Any]) -> CRUDResult:
        """Créer une nouvelle catégorie"""
        try:
            # Créer l'objet Category
            category = Category.from_dict(data)
            
            # Validation
            if not category.is_valid():
                return CRUDResult(False, None, f"Données invalides: {category.get_validation_errors()}")
            
            # Vérifier l'unicité du nom
            if self._name_exists(category.name, category.id):
                return CRUDResult(False, None, f"Une catégorie avec le nom '{category.name}' existe déjà")
            
            # Vérifier la validité du parent
            if category.parent_id and not self._category_exists(category.parent_id):
                return CRUDResult(False, None, "La catégorie parent spécifiée n'existe pas")
            
            # Insérer en base
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                insert_query = """
                    INSERT INTO categories (name, description, parent_id, is_active, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                """
                
                cursor.execute(insert_query, (
                    category.name,
                    category.description,
                    category.parent_id,
                    category.is_active,
                    category.created_at.isoformat(),
                    category.updated_at.isoformat()
                ))
                
                category.id = cursor.lastrowid
                conn.commit()
            
            self.logger.info(f"Catégorie créée: {category.name} (ID: {category.id})")
            return CRUDResult(True, category.to_dict(), "Catégorie créée avec succès")
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la création de la catégorie: {e}")
            return CRUDResult(False, None, f"Erreur lors de la création: {e}")
    
    def update_record(self, record_id: int, data: Dict[str, Any]) -> CRUDResult:
        """Mettre à jour une catégorie"""
        try:
            # Vérifier que la catégorie existe
            if not self._category_exists(record_id):
                return CRUDResult(False, None, "Catégorie non trouvée")
            
            # Créer l'objet Category avec l'ID
            data['id'] = record_id
            category = Category.from_dict(data)
            category.updated_at = datetime.now()
            
            # Validation
            if not category.is_valid():
                return CRUDResult(False, None, f"Données invalides: {category.get_validation_errors()}")
            
            # Vérifier l'unicité du nom
            if self._name_exists(category.name, category.id):
                return CRUDResult(False, None, f"Une catégorie avec le nom '{category.name}' existe déjà")
            
            # Vérifier la validité du parent et éviter les cycles
            if category.parent_id:
                if not self._category_exists(category.parent_id):
                    return CRUDResult(False, None, "La catégorie parent spécifiée n'existe pas")
                
                if self._would_create_cycle(category.id, category.parent_id):
                    return CRUDResult(False, None, "Cette modification créerait une référence circulaire")
            
            # Mettre à jour en base
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                update_query = """
                    UPDATE categories 
                    SET name = ?, description = ?, parent_id = ?, is_active = ?, updated_at = ?
                    WHERE id = ?
                """
                
                cursor.execute(update_query, (
                    category.name,
                    category.description,
                    category.parent_id,
                    category.is_active,
                    category.updated_at.isoformat(),
                    category.id
                ))
                
                conn.commit()
            
            self.logger.info(f"Catégorie mise à jour: {category.name} (ID: {category.id})")
            return CRUDResult(True, category.to_dict(), "Catégorie mise à jour avec succès")
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la mise à jour de la catégorie: {e}")
            return CRUDResult(False, None, f"Erreur lors de la mise à jour: {e}")
    
    def delete_record(self, record_id: int) -> CRUDResult:
        """Supprimer une catégorie"""
        try:
            # Vérifier que la catégorie existe
            if not self._category_exists(record_id):
                return CRUDResult(False, None, "Catégorie non trouvée")
            
            # Vérifier s'il y a des articles associés
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) as count FROM articles WHERE category_id = ?", (record_id,))
                article_count = cursor.fetchone()['count']
                
                if article_count > 0:
                    return CRUDResult(False, None, f"Impossible de supprimer: {article_count} article(s) associé(s)")
                
                # Vérifier s'il y a des catégories enfants
                cursor.execute("SELECT COUNT(*) as count FROM categories WHERE parent_id = ?", (record_id,))
                children_count = cursor.fetchone()['count']
                
                if children_count > 0:
                    return CRUDResult(False, None, f"Impossible de supprimer: {children_count} catégorie(s) enfant(s)")
            
            # Supprimer la catégorie
            return super().delete_record(record_id)
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la suppression de la catégorie: {e}")
            return CRUDResult(False, None, f"Erreur lors de la suppression: {e}")
    
    def get_hierarchy(self) -> CategoryHierarchy:
        """Obtenir la hiérarchie complète des catégories"""
        try:
            categories = []
            records = self.get_all_records()
            
            for record in records:
                category = Category.from_dict(record)
                categories.append(category)
            
            return CategoryHierarchy(categories)
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération de la hiérarchie: {e}")
            return CategoryHierarchy([])
    
    def get_root_categories(self) -> List[Dict[str, Any]]:
        """Obtenir les catégories racines (sans parent)"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM categories 
                    WHERE parent_id IS NULL AND is_active = 1
                    ORDER BY name
                """)
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des catégories racines: {e}")
            return []
    
    def get_children_categories(self, parent_id: int) -> List[Dict[str, Any]]:
        """Obtenir les catégories enfants d'une catégorie"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM categories 
                    WHERE parent_id = ? AND is_active = 1
                    ORDER BY name
                """, (parent_id,))
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des catégories enfants: {e}")
            return []
    
    def _name_exists(self, name: str, exclude_id: Optional[int] = None) -> bool:
        """Vérifier si un nom de catégorie existe déjà"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                if exclude_id:
                    query = "SELECT COUNT(*) as count FROM categories WHERE name = ? AND id != ?"
                    params = (name, exclude_id)
                else:
                    query = "SELECT COUNT(*) as count FROM categories WHERE name = ?"
                    params = (name,)
                
                cursor.execute(query, params)
                return cursor.fetchone()['count'] > 0
                
        except Exception as e:
            self.logger.error(f"Erreur lors de la vérification du nom: {e}")
            return False
    
    def _category_exists(self, category_id: int) -> bool:
        """Vérifier si une catégorie existe"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) as count FROM categories WHERE id = ?", (category_id,))
                return cursor.fetchone()['count'] > 0
                
        except Exception as e:
            self.logger.error(f"Erreur lors de la vérification de l'existence: {e}")
            return False
    
    def _would_create_cycle(self, category_id: int, new_parent_id: int) -> bool:
        """Vérifier si définir new_parent_id comme parent de category_id créerait un cycle"""
        try:
            # Remonter la hiérarchie depuis new_parent_id
            current_id = new_parent_id
            visited = set()
            
            while current_id and current_id not in visited:
                if current_id == category_id:
                    return True  # Cycle détecté
                
                visited.add(current_id)
                
                # Obtenir le parent du current_id
                with self.db_manager.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT parent_id FROM categories WHERE id = ?", (current_id,))
                    result = cursor.fetchone()
                    current_id = result['parent_id'] if result else None
            
            return False
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la vérification des cycles: {e}")
            return True  # En cas d'erreur, on considère qu'il y aurait un cycle
